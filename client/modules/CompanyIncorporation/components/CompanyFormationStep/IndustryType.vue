<template>
    <div class="card nip-active-card-border" id="sic-component">
        <div class="card-body p-0">
            <template v-if="isLoading">
                <div class=" text-center p-4">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            </template>
            <div class="row g-0" v-show="loadComponent">
                <div class="bg-white m-0 nip-guide-card-bottom-border p-3" :class="showGuide ? 'd-sm-block d-md-none d-lg-none d-xl-none d-xxl-none' : 'd-none' ">
                    <GuideComponent
                        :content="guideContent"
                        @triggerGuideAction="triggerGuideAction"
                        :customizedTitle="true"
                        :title-classes="'fs-6 nip-help-grey-text mt-5 mb-5'"
                    />
                </div>
                <div class="p-4" :class="showGuide ? 'col-md-8' : 'col-12'">
                    <div class="p-3">
                        <span role="button" @click="triggerGuideAction()" v-show="!showGuide">
                            <i class="fa-solid fa-circle-question float-end fa-lg nip-question-mark-helper"></i>
                        </span>
                        <p v-if="fieldErrors" class="alert-error rounded my-3" id="error-message">
                            <template v-if="responseStatus === 422">
                                {{ fieldErrors.sicError }}
                            </template>
                            <template v-else>
                                {{fieldErrors?.error}}
                            </template>
                        </p>
                        <div class="col-sm-12 pb-3" style="">
                            <h4 class="card-title fw-semibold">Nature of Business</h4>
                            <p class="nip-text-color fw-normal mt-4">
                                Standard Industrial Classification (SIC) codes are used by the government to track business trends in the UK.
                                Providing one is enough, but you can select up to four different codes.
                            </p>
                            <div class="bg-light px-3 pt-3 border-1">
                                <div>
                                    <ul id="nav-tabs" class="nav nav-pills">
                                        <li class="nav-item" :class="searchByKeyword ? 'active' : '' " id="search" role="presentation">
                                            <a class="nav-link link-dark" @click.prevent="toggleSearch(true)">
                                                Search by keyword
                                            </a>
                                        </li>
                                        <li class="nav-item" :class="searchByKeyword ? '' : 'active' " id="browse" role="presentation">
                                            <a class="nav-link link-dark" @click.prevent="toggleSearch(false)">
                                                Browse by category
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <br />

                            <div v-show="searchByKeyword" class="formation_sic">
                                <p class="fw-bold">Enter a keyword for your business activity</p>
                                <div class="input-group has-search position-relative" id="filter-wrapper">
                                    <span class="fa fa-search form-control-feedback nip-search-icon"></span>
                                    <input id="dt-filter" class="form-control search-input rounded-5"
                                           type="text" rv-on-keyup="filterTable" placeholder=""
                                    />
                                    <span id="clear-search" class="input-group-text bg-transparent border-0 ms-0">
                                    <a href="javascript:;" rv-on-click="clearSearch" data-filter-input="dt-filter" id="clearSearchButton">
                                        <span class="fw-semibold text-decoration-underline cms-link">Clear Search</span>
                                    </a>
                                </span>
                                </div>

                                <table id="sic-list" class="display data-table dataTable table hidden mt-0 mx-1 border-bottom border-end border-start" cellspacing="0" width="100%"
                                       style="max-width: calc(100% - 30px); margin-left: 9px"
                                >
                                    <thead>
                                    <tr class="display: none">
                                        <th>Description</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="code in sicCodes" :data-code="code.Code">
                                        <td class="small" @click="addSicCode(code.Code)" role="button">{{code.Description}} (code: {{code.Code}})</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div v-show="!searchByKeyword" class="col-sm-12 formation_sic2" style="">
                                <p class="fw-bold">Find your principal business activity by selecting a category and selecting from the Standard Industrial Classification (SIC) codes.</p>
                                <div class="table-responsive">
                                    <table id="sic-list2" class="table border" style="margin-bottom: 0 !important;" cellspacing="0" width="100%">
                                        <col>
                                        <col width="380">
                                        <col width="150">
                                        <thead class="hidden"></thead>
                                        <tbody>
                                        <tr v-for="code in sicCodes" :data-code="code.Code">
                                            <td class="">{{code.Code}}</td>
                                            <td class="">{{code.Description}}</td>
                                            <td class="">{{code.Section}}</td>
                                            <td class="btn-link link-primary fw-semibold" @click="codeAlreadyAdded(code.Code) ? removeSicCode(code.Code) : addSicCode(code.Code)" role="button">
                                              {{
                                                codeAlreadyAdded(code.Code) ? 'Remove' : 'Add'
                                              }}
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <br/>

                            <div v-if="companySicCodes && companySicCodes.length > 0" class="fw-semibold fs-5 mb-4 mt-1">Your SIC codes</div>
                            <div id="dt-company-sic-summary" class="mt-0" style="margin-right: 3em; width: 100%;">
                                    <div v-for="code in companySicCodes">
                                            <div class="mb-3" style="border-bottom: 0;">
                                                <span class="fw-semibold text-decoration-underline cms-link" @click="removeSicCode(code.Code)" style="cursor: pointer; width: fit-content;">Remove</span>
                                                <span class="ml-3 text-black nip-font-inter">{{ code.Code }}: {{ code.Description }}</span>
                                            </div>
                                    </div>
                            </div>
                            <div class="row">
                                <div class="col-12 flex-align-end">
                                    <button type="submit" id="industryTypeContinueButton" class="btn nip-btn-orange float-end nip-next-step-button d-flex align-items-center" :disabled="!hasCodeSelected()" @click="goToNextStep()">
                                        {{ continueButtonLabel }}
                                        <span class="arrow">
                                            <span class="arrow-shaft arrow-shaft-color-override-white"></span>
                                            <span class="arrow-head arrow-head-color-override-white"></span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="bg-white m-0 nip-guide-card-border p-3" :class="showGuide ? 'd-lg-block d-xl-block d-md-block d-xxl-block d-none col-md-4' : 'd-none'">
                    <GuideComponent
                        :content="guideContent"
                        @triggerGuideAction="triggerGuideAction"
                        :customizedTitle="true"
                        :title-classes="'fs-6 nip-help-grey-text mt-5 mb-5'"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.active {
    background-color: white !important;
    border-top-right-radius: 0.25rem !important;
    border-top-left-radius: 0.25rem !important;
    border-bottom: 0 !important;
}
.active-option a {
    color: #001f4e!important;
}
</style>

<script>
import Vue from "vue";
import api from "./../../Api";
import { SicController } from './../../../../js/modules/Incorporation/SicController';
import GuideComponent from './../global/GuideComponent.vue'
import {EVENT_KEY_APPOINTMENTS, EVENT_KEY_COMPANY_FORMATION} from "@/modules/CompanyIncorporation/constants/eventKeys";
import {createLog} from "@/modules/CompanyIncorporation/listeners/logEventListener";
import {
    COMPANY_INCORPORATION_COMPANY_FORMATION,
    SIC_LOAD,
    SIC_SUBMIT
} from "@/modules/CompanyIncorporation/constants/stepEnums";
import rivets from 'rivets';

export default Vue.extend({
    data () {
        return {
            info: null,
            isLoading: false,
            showGuide: false,
            sicCodes: [],
            companySicCodes: [],
            fieldErrors: [],
            loadComponent: false,
            responseStatus: 0,
            guideContent: `
                <p class="mb-3 nip-help-grey-text lh-base">Standard Industrial Classification (SIC) codes are used to define your company's principal business activities.
                You need to specify at least one activity, although you can add up to 4.</p>
                <p class="nip-link"><a href="https://resources.companieshouse.gov.uk/sic/" target="_blank">Learn more about SIC codes</a></p>
            `,
            searchByKeyword: true,
        }
    },
    components: {
        GuideComponent
    },
    props: {
        companyId: String,
        continueButtonLabel: String,
    },
    methods: {
        getIndustryTypeData(){
            this.isLoading = true
            api.get(`/api/incorporation/${this.companyId}/industry_type`)
            .then(response => {
                this.info = response
                this.sicCodes = this.info.data.sicCodes
                this.companySicCodes = this.info.data.companySicCodes
                setTimeout(function() {

                    SicController.initSearch('.formation_sic', '#sic-list')
                    SicController.initBrowse('.formation_sic2', '#sic-list2')

                    const sicListWrapper = document.getElementById('sic-list_wrapper')
                    sicListWrapper.style.width = 'calc(100% - 112px)'
                    sicListWrapper.style.top = '-12px'
                    sicListWrapper.style.color = '#2B2B2B'

                    const sicList = document.getElementById('sic-list')
                    sicList.classList.add('light-gray-border')
                    sicList.classList.remove('hidden')

                    const sicList2 = document.getElementById('sic-list2')
                    sicList2.classList.add('light-gray-border')
                    sicList2.classList.remove('hidden')

                }, 500);
                this.checkIfInputIsEmpty()
            })
            .catch(error => {
                this.errored = true
            })
            .finally(response => {
                this.isLoading = false
                this.loadComponent = true
            })
        },
        redirectToErrorDiv() {
          const errorDiv = document.getElementById('error-message');
          if (!errorDiv) return

          window.scrollTo({
            top: errorDiv.getBoundingClientRect().top + window.scrollY - 200,
            behavior: 'smooth'
          })
        },
        codeAlreadyAdded(code) {
          return this.companySicCodes.flatMap((codeDetails) => codeDetails.Code).includes(code)
        },
        async addSicCode(code) {
            let codeData = {
                code: code,
            }
            document.body.style.cursor='wait'
            const formData = new FormData();
            formData.append("codeData", JSON.stringify(codeData))
            await api.post(`/api/incorporation/${this.companyId}/add_sic_code/`, formData)
            .then(response => {
                this.responseStatus = response.status
                this.companySicCodes = response.data.companySicCodes
                this.fieldErrors = null
            })
            .catch(error => {
                this.fieldErrors = error.response.data
                this.responseStatus = error.response.status
            })
            .finally(response => {
                const clearSearchButton = document.getElementById('clearSearchButton')
                clearSearchButton.click();
                document.body.style.cursor='default'
                this.redirectToErrorDiv()
            })
        },
        async removeSicCode(code) {
            let codeData = {
                code: code,
            }
            const formData = new FormData();
            formData.append("codeData", JSON.stringify(codeData))
            await api.post(`/api/incorporation/${this.companyId}/remove_sic_code/`, formData)
            .then(response => {
                this.fieldErrors = null
                this.responseStatus = response.status
                this.companySicCodes = response.data.companySicCodes
            })
            .catch(error => {
                this.fieldErrors = error.response.data
                this.responseStatus = error.response.status
            })
            .finally(response => {
              this.redirectToErrorDiv()
            })
        },
        checkIfInputIsEmpty() {
            const input = document.getElementById('dt-filter')
            const list = document.getElementById('sic-list')

            if (input.value.trim() === '') {
                list.style.display = 'none'
            } else {
                list.style.display = ''
            }
        },
        async goToNextStep() {
            if (!this.hasCodeSelected) {
                return;
            }

            await createLog(this.companyId, {
                newIncorporationProcess: true,
                step: COMPANY_INCORPORATION_COMPANY_FORMATION,
                subStep: SIC_SUBMIT,
                data: JSON.stringify({...this.$data, ...this.$props}),
            });

            await api.post(`/api/incorporation/incorporation_events/log_step_filled_event/${this.companyId}/`, {
                eventKey: EVENT_KEY_COMPANY_FORMATION,
                nextStepKey: EVENT_KEY_APPOINTMENTS
            });

            const sicCodeChanged = new CustomEvent("sic_code_change", {
            bubbles: true,
            detail: { companySicCodes: this.companySicCodes},
            })
            document.dispatchEvent(sicCodeChanged);

        },
        hasCodeSelected() {
            return this.companySicCodes !== undefined && this.companySicCodes.length > 0
        },
        triggerGuideAction() {
            this.showGuide = !this.showGuide;
        },
        toggleSearch(searchByKeyword) {
            this.searchByKeyword = searchByKeyword
        }
    },

    async mounted () {
        this.getIndustryTypeData();
        document.getElementById('dt-filter').addEventListener('keyup', this.checkIfInputIsEmpty);
        document.getElementById('clear-search').addEventListener('click', this.checkIfInputIsEmpty);

        await createLog(this.companyId, {
            newIncorporationProcess: true,
            step: COMPANY_INCORPORATION_COMPANY_FORMATION,
            subStep: SIC_LOAD,
            data: JSON.stringify({...this.$data, ...this.$props}),
        });
    },
});
</script>