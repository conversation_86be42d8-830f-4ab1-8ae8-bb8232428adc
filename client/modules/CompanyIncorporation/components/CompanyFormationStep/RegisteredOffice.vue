<template>
    <div class="card nip-active-card-border" id="registered-office-component">
        <div class="card-body p-0">
            <template v-if="isLoading">
              <div class=" text-center p-4">
                <div class="spinner-border" role="status">
                  <span class="sr-only">Loading...</span>
                </div>
              </div>
            </template>
            <div class="row g-0" v-show="loadComponent">
                <div class="bg-white m-0 nip-guide-card-bottom-border p-3" :class="showGuide ? 'd-sm-block d-md-none d-lg-none d-xl-none d-xxl-none' : 'd-none' ">
                    <GuideComponent
                        :content="guideContent"
                        :customizedTitle="true"
                        :title-classes="'fs-6 nip-help-grey-text mt-5 mb-5'"
                        @triggerGuideAction="triggerGuideAction"
                    />
                </div>
                <div class="p-4" :class="showGuide ? 'col-md-8' : 'col-12' ">
                    <div class="p-3">
                      <span role="button" @click="triggerGuideAction()" v-show="!showGuide">
                          <i class="fa-solid fa-circle-question float-end fa-lg nip-question-mark-helper"></i>
                      </span>
                      <p v-if="fieldErrors && fieldErrors?.length > 0" class="alert-error rounded my-3">
                          <template v-if="responseStatus === 400 && errorsCount() > 0">
                              Form has <b> {{ errorsCount() }}</b> error(s). See below for more details:
                          </template>
                          <template v-else>
                              <b>An internal error occurred. Please try again or contact support with this message:</b><br />
                              {{fieldErrors?.error}}
                          </template>
                      </p>

                      <form name="registered_office_form" method="post" id="registered_office_form" v-on:submit.prevent="onSubmit">
                          <p class="fs-3 fw-semibold">Registered Addresses</p>

                          <div v-show="canShowNewConfirmationStatementFields"
                              class="col-sm-12"
                              id="fieldset_1343"
                          >
                              <p class="fw-semibold fs-5">Registered Email Address</p>

                              <p class="nip-text-color">You must provide an email address for your company and monitor incoming emails. This address will not appear on the public register but may be used by Companies House to contact you.</p>

                              <div class="row mb-3">
                                  <div class="col-md-4 fw-semibold pt-2">
                                      Registered Email Address
                                  </div>

                                  <div class="col-md-6">
                                      <input
                                          type="email"
                                          v-model="registeredEmailAddressValue"
                                          id="registeredEmailAddress"
                                          name="registeredEmailAddress"
                                          class="form-control"
                                          placeholder="Enter email address"
                                      >
                                  </div>
                                  <div class="offset-md-4 col-md-6 mt-2" v-show="showRegisteredEmailAddressSuggestion">
                                      Suggestion:
                                      <span class="py-1 px-2 border rounded" role="button" @click="useRegisteredEmailAddressSuggestion()">
                                          {{ registeredEmailAddressSuggestion }}
                                      </span>
                                  </div>
                                  <div class="col-sm-12 has-error text-danger">
                                      <div v-for="error in fieldErrors?.registeredEmailAddress" class="help-block">{{ error }}</div>
                                  </div>
                              </div>
                          </div>
	                      
	                      <hr class="col-12 my-4">

	                      <p class="fw-semibold fs-5 mb-3">
                              Registered Office Address
                          </p>

                          <div v-if="hasRegisteredOfficeId"
                              class="col-sm-12"
                              id="fieldset_0"
                          >
                              <p class="fs-5 fw-semibold pt-3">Use our registered office address included with your package</p>
                              <label for="ourRegisteredOffice">
                                <input
                                    class="form-check-input"
                                    type="checkbox"
                                    value="1"
                                    id="ourRegisteredOffice"
                                    name="ourRegisteredOffice"
                                    :checked="hasMSGRegisteredOffice"
                                    data-gtm-form-interact-field-id="0"
                                    @change="showAddressForm($event)"
                                />
                                <span class="ms-1">{{ msgAddress }}</span>
                              </label>
                              <div v-if="hasMSGRegisteredOffice && this.upsellTypeChecked === '-1' && this.showROMessage" class="mt-2" id="ROMessage">
                                  <p>
                                      <i
                                          class="fa fa-exclamation-circle em13 text-danger"
                                          data-bs-toggle="tooltip"
                                          data-bs-placement="bottom"
                                          data-bs-custom-class="custom-tooltip"
                                          title="Your home address will remain private and our address will
                                              show on the public register affording you an additional layer of privacy."
                                          data-bs-html="true"
                                      ></i>
                                      <span class="mx-1">
                                          The address you use here as your service address will be listed online at the Companies House public register and searchable worldwide
                                      </span>
                                  </p>
                              </div>
                          </div>

                          <template v-if="registeredOfficeUpsellOffer">
                            <div>
                              <p class="nip-text-color">
                                Your Registered Office address is the official address of your company, and will be publicly available on the online register. Keep your address private by using our registered office address service.
                              </p>
	                            
                            <div class="alert nip-alert-background-color d-flex align-items-center" role="alert">
	                            <img :src="infoWithCircle" alt="Information icon" class="icon">
	                            <div class="ms-3">
		                            <span class="fw-bold nip-2b-color">Protect yourself: </span>
		                            <span class="nip-text-color">Use the Privacy Service to keep your address private.</span>
	                            </div>
                            </div>

                              <div class="form-group mt-2">
                                <div class="radio">
                                  <label for="registered_office_form_registeredOfficeUpsellType_0" class="fw-semibold required d-flex align-items-start">
                                    <input type="radio" id="registered_office_form_registeredOfficeUpsellType_0"
                                          class="form-check-input nip-radio-icon nip-radio-large"
                                          name="registered_office_form[registeredOfficeUpsellType]"
                                          v-model="upsellTypeChecked"
                                          required="required"
                                          :value="registeredOfficeUpsellId"
                                          data-gtm-form-interact-field-id="0"
                                           style="min-width: 32px">
                                    <div class="ms-4 nip-large-text">
                                      I want to use Companies Made Simple Privacy Services for only £1.99/month *
                                    </div>
                                  </label>
                                  <div class="fw-light ms-5">
                                    <p class="mt-2"><small class="nip-small-text-color">* Excludes VAT. Valid for the first 12 months. Usually £6.99 per month.</small></p>
                                    <div class="mt-4">
                                        <p><i class="fa fa-check nip-check-icon"></i><span class="nip-text-color">Prevent your home address from appearing on the public register with our Registered Office Address service.</span></p>
                                        <p><i class="fa fa-check nip-check-icon"></i><span class="nip-text-color">Protect the home address of all company officers with our Service Address</span></p>
                                        <p><i class="fa fa-check nip-check-icon"></i><span class="nip-text-color">Project a professional image with a prestigious London business address.</span></p>
                                    </div>
                                      <p class="fw-semibold text-decoration-underline cms-link nip-font-inter"
                                        data-bs-toggle="modal"
                                        data-bs-target="#registered-office-compare-modal"
                                        role="button"
                                      >
                                          Compare
                                      </p>

                                    <div v-show="upsellTypeChecked && upsellTypeChecked !== '-1'" class="col-sm-12">
                                      <div class="pt-4">
                                        <p class="fw-semibold">How you are listed using the Privacy Service</p>
                                        <p><small>Your Name, 20 Wenlock Road, London, N1 7GU</small></p>
                                      </div>

                                      <div id="inline-payment" class="col-sm-6">
                                        <div id="omnipay-inline-payment-component" :data-inline-payment="omnipayComponentData"></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <div class="col-sm-12 has-error text-danger">
                                    <div v-for="error in fieldErrors?.registeredOfficeUpsellType" class="help-block">{{ error }}</div>
                                </div>
                              </div>

                              <div class="form-group">
                                <div class="radio">
                                  <label for="registered_office_form_registeredOfficeUpsellType_1" class="fw-semibold required d-flex align-items-start">
                                    <input type="radio" id="registered_office_form_registeredOfficeUpsellType_1"
                                          class="form-check-input nip-radio-icon nip-radio-large"
                                          name="registered_office_form[registeredOfficeUpsellType]"
                                          v-model="upsellTypeChecked"
                                          required="required"
                                          value="-1"
                                          :checked="address"
                                           style="min-width: 32px">
                                    <div class="ms-4 nip-large-text">
                                      I will use my own address
                                    </div>
                                  </label>
                                  <div class="alert nip-red-alert-background-color d-flex align-items-center mt-3" role="alert" v-if="upsellTypeChecked === '-1'">
                                  <img :src="alertIcon" alt="Danger icon" class="icon">
                                  <div class="ms-3">
                                    <span class="fw-bold nip-2b-color">Important: </span>
                                    <span class="nip-text-color">Using your personal address as a Registered Office will expose it on the public register, leading to unwanted visitors, junk mail, and potential ID fraud. If you rent, ensure you have your landlord's permission for this purpose.</span>
                                  </div>
                                </div>
                                </div>
                              </div>
                            </div>
                          </template>

                        <div v-show="upsellTypeChecked === '-1' && !hasMSGRegisteredOffice" class="col-sm-12 ms-5">
                          <address-component
                              ref="address_component_reference"
                              :should-render-address="upsellTypeChecked === '-1'"
                              :prefill="prefill"
                              :field-errors="fieldErrors"
                              form-id="registered_office_form"
                              :address="address"
                              @prefillAddress="prefillAddress"
                          />
                        </div>

                        <div class="col-sm-12 has-error text-danger">
                          <div v-for="error in fieldErrors?.registeredOfficeType" class="help-block">{{ error }}</div>
                        </div>

                          <div class="col-12" v-show="canShowCertificate">
                              <div class="row">
                                  <div class="col-12">
                                      <p class="mt-3 mb-2 fw-semibold">How do you want to receive your Incorporation Certificate?</p>
                                  </div>
                                  <div class="col-12 has-error mb-2">
                                      <div class="help-block text-danger">

                                      </div>
                                  </div>
                                  <div class="col-sm-12">
                                      <input type="radio" v-model="certificatePrinted" name="certificatePrinted" :value="false" id="certificatePrinted0" class="me-2">
                                      <label for="certificatePrinted0">Digitally, by email</label>
                                      <br>
                                      <input type="radio" v-model="certificatePrinted" name="certificatePrinted" :value="true" id="certificatePrinted1" class="me-2">
                                      <label for="certificatePrinted1">Physically, by post</label><br>
                                  </div>
                              </div>
                          </div>

                          <div class="row m-3">
                              <div class="col-md-7">
                              </div>
                              <div class="col-md-5">
                                  <button type="submit" class="btn nip-btn-orange nip-next-step-button float-end d-flex align-items-center" id="registeredOfficeContinueButton">
                                      Submit & Continue
                                      <span class="arrow">
                                          <span class="arrow-shaft arrow-shaft-color-override-white"></span>
                                          <span class="arrow-head arrow-head-color-override-white"></span>
                                      </span>
                                  </button>
                              </div>
                          </div>
                      </form>
                    </div>
                </div>
                <div class="bg-white m-0 nip-guide-card-border p-3" :class="showGuide ? 'd-lg-block d-xl-block d-md-block d-xxl-block d-none col-md-4' : 'd-none' ">
                    <GuideComponent
                        :content="guideContent"
                        :customizedTitle="true"
                        :title-classes="'fs-6 nip-help-grey-text mt-5 mb-5'"
                        @triggerGuideAction="triggerGuideAction"
                    />
                </div>
            </div>
        </div>
	    
      <!-- modal begin -->
      <div class="modal fade" id="registered-office-compare-modal" style="z-index: 100000;">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header px-4">
              <p class="modal-title fs-5 mt-2" id="registered-office-compare-modal-label">Choose your Registered Office</p>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                <i class="fa-solid fa-xmark"></i>
              </button>
            </div>
            <div class="modal-body px-4">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                  <tr>
                    <th></th>
                    <th class="fw-semibold text-center">Full Privacy</th>
                    <th class="fw-semibold text-center">Your own address</th>
                  </tr>
                  </thead>
                  <tbody class="border">
                  <tr class="">
                    <td>
                      <p class="ms-3 mt-2 fs-6 mb-2">Registered Office Address (N1)</p>
                      <small class="ms-3 text-muted">Avoid using your home address as your  company's registered office</small>
                    </td>
                    <td class="text-center">
                      <i class="pt-3 fa fa-check text-cms-blue" aria-hidden="true"></i>
                    </td>
                    <td class="text-center"><i class="fa fa-exclamation-triangle fs-4 text-danger" aria-hidden="true"></i><br><span class="small text-muted">address publicly available</span></td>
                  </tr>
                  <tr>
                    <td>
                      <p class="ms-3 mt-2 fs-6 mb-2">Directors Addresses Protected</p>
                      <small class="ms-3 text-muted">Hidden from the public register</small>
                    </td>
                    <td class="text-center"><i class="pt-3 fa fa-check text-cms-blue" aria-hidden="true"></i></td>
                    <td class="text-center"><i class="fa fa-exclamation-triangle fs-4 text-danger" aria-hidden="true"></i><br><span class="small text-muted">address publicly available</span></td>
                  </tr>
                  <tr>
                    <td>
                      <p class="ms-3 mt-2 fs-6 mb-2">Secretaries Addresses Protected</p>
                      <small class="ms-3 text-muted">Hidden from the public register</small>
                    </td>
                    <td class="text-center"><i class="pt-3 fa fa-check text-cms-blue" aria-hidden="true"></i></td>
                    <td class="text-center"><i class="fa fa-exclamation-triangle fs-4 text-danger" aria-hidden="true"></i><br><span class="small text-muted">address publicly available</span></td>
                  </tr>
                  <tr>
                    <td>
                      <p class="ms-3 mt-2 fs-6 mb-2">Shareholders Addresses Protected</p>
                      <small class="ms-3 text-muted">Hidden from the public register</small>
                    </td>
                    <td class="text-center"><i class="pt-3 fa fa-check text-cms-blue" aria-hidden="true"></i></td>
                    <td class="text-center"><i class="fa fa-exclamation-triangle text-danger fs-4" aria-hidden="true"></i><br><span class="small text-muted">address publicly available</span></td>
                  </tr>
                  <tr>
                    <td>
                      <p class="ms-3 mt-2 fs-6 mb-2">Government mail scanned and emailed</p>
                      <small class="ms-3 text-muted">Mail from Companies House & HMRC scanned and then emailed free of charge</small>
                    </td>
                    <td class="text-center"><i class="pt-3 fa fa-check text-cms-blue" aria-hidden="true"></i></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>
                      <p class="ms-3 mt-2 fs-6 mb-2">Junk mail blocked</p>
                      <small class="ms-3 text-muted">We'll filter out any unsolicited mail</small>
                    </td>
                    <td class="text-center"><i class="pt-3 fa fa-check text-cms-blue" aria-hidden="true"></i></td>
                    <td></td>
                  </tr>
                  </tbody>
                </table>
              </div>
              <p class="fs-5 fw-semibold mt-3">
                <i class="fa fa-thin fa-circle-check fa-2xl me-3 pb-3" style="color:#95c158"></i>
                <span class="margin-left-6">Proof of ID</span>
              </p>
              <p>We are legally obliged to check proof of ID and proof of address for customers using our address services. This is to comply with the Know Your Customer (KYC) and Anti-Money Laundering (AML) regulations. For which documents you need to provide us please check the <a href="https://support.companiesmadesimple.com/hc/en-us/articles/************-Proof-of-ID-and-Address-Requirements-Comprehensive-Guide-" target="_blank">FAQs</a>.</p>
              <p class="fs-5 fw-semibold mt-4 mb-2">How it works</p>
              <p><strong>Protect your company's privacy with a prestigious London address</strong><br>
                Our N1 address will appear on Companies House public register as your company's official registered office instead of your residential address. Your official government mail (from Companies House, HMRC, Government Gateway) is safely opened, scanned and then emailed to you within 24 hours of being received at our office, at no extra charge</p>
              <p>If your customer turns up at our address, we will explain that this is your registered office address but you don't trade from this location. Where possible, we will supply them with an office address or telephone number listed on your website. We never disclose your private  address or contact information.</p>
              <p><strong>Prevent junk mail, only receive your company's statutory mail</strong><br>
                Newly formed companies are often targeted by marketing companies and junk mailers who buy lists of company addresses. We'll filter your mail to stop you being inundated with lots of unsolicited post.</p>
              <p><strong>Ideal for international customers or if you have rental agreement restrictions</strong><br>
                The privacy services are a perfect solution if you do not have a UK address. Many rental agreements also prevent the use of the rented address as a registered office. If not explicitly prevented, permission must be sought from the landlord which can be difficult to get.</p>
              <p class="fs-5 fw-semibold mt-4 mb-2">FAQs</p>
              <p><strong>Can you post my mail?</strong><br>
                We'll scan and then email all your official government mail, ensuring you get to see it on the same day that we receive it. If you would like to receive the actual letter too, simply email this <NAME_EMAIL> within 7 days of getting the scan.</p>
              <p><strong>Can I use this service if I'm an International customer?</strong><br>
                Yes, this service is perfect if you do not have a UK address.</p>
              <p><strong>What happens if I want to cancel the service?</strong><br>
                If you no longer wish to use our address as your company's Registered Office, you can simply cancel the service from your online account and enter your company's new Registered Office address.</p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-default text-cms-blue" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
      <!-- modal end -->
	    
      <DialogModal
        modalId="choose-ro-modal"
        title="This address will be publicly listed at Companies House and searchable online"
        mainText="Companies House requires the registered office address to be published on the online public register. This address can be seen online, by anyone, worldwide. It could lead to unwanted mail, cold callers, and serious privacy concerns including identity theft."
        rejectButtonText="Accept the risk"
        acceptButtonText="Protect my privacy"
        @rejectCallback="handleRejectCallback"
        @acceptCallback="handleAcceptCallback"
        :useRejectCallbackToCloseModal="false"
      />
    </div>
</template>

<script>
import alertIcon from "@/assets/images/alert.svg";
import infoWithCircle from "@/assets/images/info-with-circle.svg";
import {
  COMPANY_INCORPORATION_COMPANY_FORMATION,
  OFFICE_LOAD, OFFICE_SUBMIT
} from "@/modules/CompanyIncorporation/constants/stepEnums";
import { createLog } from "@/modules/CompanyIncorporation/listeners/logEventListener";
import Vue, { ref } from "vue";
import DialogModal from "../global/DialogModal.vue";
import api from "./../../Api";
import AddressComponent from './../AddressComponent.vue';
import GuideComponent from './../global/GuideComponent.vue';
import StepButton from './../StepButton.vue';

export default Vue.extend({
    data () {
        return {
          infoWithCircle,
          alertIcon,
          info: null,
          domain: window.location.origin,
          isLoading: false,
          showGuide: false,
          loadComponent: false,
          canShowCertificate: false,
          certificatePrinted: false,
          registeredOfficeUpsellOffer: false,
          registeredOfficeUpsellId: '',
          registeredOfficeUpsellType: '',
          registeredEmailAddressValue: '',
          fieldErrors: {},
          prefill: {},
          address: {},
          responseStatus: 0,
          upsellTypeChecked: '',
          canShowNewConfirmationStatementFields: false,
          msgAddress: '',
          hasMSGRegisteredOffice: false,
          hasRegisteredOfficeId: false,
          showROMessage: false,
          isWholesale: false,
          shouldShowModal: true,
          guideContent: `
            <p class="mb-3 nip-help-grey-text lh-base">This address will be used for statutory mail from Companies House and HM Revenue & Customs.</p>
            <p class="nip-help-grey-text lh-base">Your registered office is required to be in England & Wales, Scotland, or Northern Ireland.</p>
            <p class="nip-help-grey-text lh-base">The location of the registered office at incorporation determines where the company is registered. E.g. If registered in England, the registered office can never be changed to Scotland or Northern Ireland.</p>
          `,
      }
    },
    components: {
        AddressComponent,
        StepButton,
        GuideComponent,
        DialogModal
    },
    props: {
        companyId: String,
        omnipayComponentData: String,
        omnipayUrl: String,
        continueButtonLabel: String,
        registeredEmailAddressSuggestion: String
    },
    setup() {
        const address_component_reference = ref(null)
        return {
            address_component_reference
        }
    },
    methods: {
        getRegisteredOfficeData() {
            this.isLoading = true
            api.get(`/api/incorporation/${this.companyId}/registered_office`)
            .then(response => {
                this.info = response
                this.prefill = this.info.data.prefillAddress
                this.address = this.info.data.addressFields
                this.registeredOfficeUpsellType = this.info.data.registeredOfficeUpsellType
                this.registeredOfficeUpsellId = this.info.data.registeredOfficeUpsellId
                this.registeredOfficeUpsellOffer = this.info.data.registeredOfficeUpsellOffer
                this.registeredEmailAddressValue = this.info.data.registeredEmailAddressValue
                this.canShowNewConfirmationStatementFields = this.info.data.canShowNewConfirmationStatementFields
                this.msgAddress = this.info.data.msgAddress
                this.hasMSGRegisteredOffice = this.info.data.hasMSGRegisteredOffice
                this.hasRegisteredOfficeId = this.info.data.hasRegisteredOfficeId
                this.showROMessage = !this.info.data.hasMSGRegisteredOffice
                this.canShowCertificate = this.info.data.canShowCertificatePrintedQuestion
                this.certificatePrinted = this.info.data.certificatePrinted ?? false
                this.isWholesale = this.info.data.isWholesale ?? false
                
                
                // this.upsellTypeChecked = this.registeredOfficeUpsellId
                if (this.hasRegisteredOfficeId) {
                    if (this.hasMSGRegisteredOffice) {
                        this.upsellTypeChecked = this.registeredOfficeUpsellId
                    } else {
                        this.upsellTypeChecked = '-1'
                    }
                } else if (!this.registeredOfficeUpsellOffer && this.address?.premise !== "") {
                    this.upsellTypeChecked = '-1'
                }
            })
            .catch(error => {
                this.errored = true
            })
            .finally(() => {
                this.loadComponent = true
                this.isLoading = false
                const inlinePaymentDiv = document.getElementById('inline-payment')
                if (inlinePaymentDiv) {
                    const omnipayScript = document.createElement("script");
                    omnipayScript.setAttribute(
                    "src",
                    `${this.omnipayUrl}packs/js/inline_payment_component.js`
                    );
                    omnipayScript.async = true;
                    inlinePaymentDiv.appendChild(omnipayScript);
                }
            })
        },
        handleOpenModal({ modalId }) {
          $(`#${modalId}`).modal('show')
        },
        handleRejectCallback({ modalId }) {
          $(`#${modalId}`).modal('hide')
          this.shouldShowModal = false
          this.onSubmit()
        },
        handleAcceptCallback({ modalId }) {
          this.upsellTypeChecked = this.registeredOfficeUpsellId;
          if (this.hasRegisteredOfficeId) {
            this.hasMSGRegisteredOffice = true;
            this.showROMessage = false;
          }
          $(`#${modalId}`).modal('hide')
        },
        handleCloseCallback({ modalId }) {
          $(`#${modalId}`).modal('hide')
        },
        async onSubmit() {
          if (!this.upsellTypeChecked) {
              this.fieldErrors = {
                registeredOfficeType: ['Please select one of the Registered Office options above']
              }
              return
            }

            if(!this.isWholesale && this.upsellTypeChecked === '-1' && this.shouldShowModal) {
                this.handleOpenModal({ modalId: 'choose-ro-modal' })
                return
            }
            this.shouldShowModal = true

            let registeredOfficeForm = {
                registeredOfficeUpsellType: this.upsellTypeChecked,
            }
            if (this.upsellTypeChecked === '-1') {
                this.updateAddress()
                registeredOfficeForm.premise       = this.address_component_reference.premise_address.value
                registeredOfficeForm.street        = this.address_component_reference.street_address.value
                registeredOfficeForm.thoroughfare  = this.address_component_reference.thoroughfare_address.value
                registeredOfficeForm.post_town     = this.address_component_reference.post_town_address.value
                registeredOfficeForm.county        = this.address_component_reference.county_address.value
                registeredOfficeForm.postcode      = this.address_component_reference.postcode_address.value?.trim()?.replace(/\s{2,}/g, ' ')
                registeredOfficeForm.country       = this.address_component_reference.country_address.value
            }

            const ourRegisteredOfficeCheckbox = document.getElementById('ourRegisteredOffice')
            if (ourRegisteredOfficeCheckbox && ourRegisteredOfficeCheckbox.checked) {
                registeredOfficeForm.ourRegisteredOffice = 1
            }

            const registeredEmailAddressTextfield = document.getElementById('registeredEmailAddress')
            if (registeredEmailAddressTextfield) {
                registeredOfficeForm.registeredEmailAddress = registeredEmailAddressTextfield.value
            }

            if (this.canShowCertificate) {
                registeredOfficeForm.certificatePrinted = this.certificatePrinted
            }

            const formData = new FormData();
            formData.append("registeredOfficeFormData", JSON.stringify(registeredOfficeForm))
            await api.post(`/api/incorporation/${this.companyId}/save_registered_office/`, formData)
            .then(response => {
                this.responseStatus = response.status
                this.fieldErrors = null
                this.hasRegisteredOfficeId = response.data.hasRegisteredOfficeId
                this.registeredOfficeUpsellOffer = response.data.registeredOfficeUpsellOffer
                this.hasMSGRegisteredOffice = response.data.hasMSGRegisteredOffice
                this.address = response.data.addressFields

                const companyAddressChanged = new CustomEvent("company_address_change", {
                    bubbles: true,
                    detail: { companyAddress: response.data.companyAddress, hasMSGRegisteredOffice: this.hasMSGRegisteredOffice },
                })
                document.dispatchEvent(companyAddressChanged)
            })
            .catch(error => {
                this.fieldErrors = error.response.data
                this.responseStatus = error.response.status
            });

            await createLog(this.companyId, {
                newIncorporationProcess: true,
                step: COMPANY_INCORPORATION_COMPANY_FORMATION,
                subStep: OFFICE_SUBMIT,
                data: JSON.stringify({...this.$data, ...this.$props}),
            });
        },
        updateAddress() {
            this.address = {
                premise:      this.address_component_reference.premise_address.value,
                street:       this.address_component_reference.street_address.value,
                thoroughfare: this.address_component_reference.thoroughfare_address.value,
                post_town:     this.address_component_reference.post_town_address.value,
                county:       this.address_component_reference.county_address.value,
                postcode:     this.address_component_reference.postcode_address.value,
                country:      this.address_component_reference.country_address.value,
            }
        },
        prefillAddress(prefillAddress){
            this.address = {
                premise:      prefillAddress.premise,
                street:       prefillAddress.street,
                thoroughfare: prefillAddress.thoroughfare,
                post_town:     prefillAddress.post_town,
                county:       prefillAddress.county,
                postcode:     prefillAddress.postcode,
                country:      prefillAddress.country,
            }
        },
        errorsCount() {
            if (!this.fieldErrors) return 0
            let count = 0
            for (let item in this.fieldErrors) {
                if (item !== 'error' && item !== 'status') {
                count += this.fieldErrors[item].length
                }
            }
            return count
        },
        showAddressForm(event) {
            if (event.target.checked) {
                this.upsellTypeChecked = this.registeredOfficeUpsellId;
                this.hasMSGRegisteredOffice = true;
                this.showROMessage = false;
            } else {
                this.upsellTypeChecked = '-1';
                this.hasMSGRegisteredOffice = false;
                this.showROMessage = true;
            }
        },
        triggerGuideAction() {
            this.showGuide = !this.showGuide;
        },
        useRegisteredEmailAddressSuggestion() {
            this.registeredEmailAddressValue = this.registeredEmailAddressSuggestion
        },
        handleMailForwardingOfferTaken() {
            this.getRegisteredOfficeData();
        },

    },
    computed: {
        showRegisteredEmailAddressSuggestion() {
            return (this.registeredEmailAddressValue || '').trim() === '' &&
                (this.registeredEmailAddressSuggestion || '') !== ''
        }
    },
    async mounted () {
        this.getRegisteredOfficeData();
        await createLog(this.companyId, {
            newIncorporationProcess: true,
            step: COMPANY_INCORPORATION_COMPANY_FORMATION,
            subStep: OFFICE_LOAD,
            data: JSON.stringify({...this.$data, ...this.$props}),
        });
        document.addEventListener('incorporation_mail_forwarding_offer_payment_succeeded', this.handleMailForwardingOfferTaken);
    },

    beforeDestroy() {
        document.removeEventListener('incorporation_mail_forwarding_offer_payment_succeeded', this.handleMailForwardingOfferTaken);
    },
});
</script>