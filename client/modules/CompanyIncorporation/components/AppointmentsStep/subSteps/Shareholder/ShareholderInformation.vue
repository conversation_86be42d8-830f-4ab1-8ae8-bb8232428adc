<template>
    <div v-if="isLoadingData">
        <div class="card p-4 border-0 nip-active-card-border">
            <div class="card-body">
                <p class="fw-semibold fs-5 card-title"> Shareholder Information </p>
                <div class=" text-center p-4">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div v-else class="my-3">
        <template v-if="showSummary">
            <ShareholderSummary
                :shares="shares"
                :has-security="hasSecurityQuestions"
                @editShareholder="handleEditing"
            />
        </template>
        <template v-else>
            <div class="card border-0 nip-active-card-border">
                <div class="card-body p-0 nip-text-color">
                    <div class="row g-0">
                        <div class="bg-white m-0 nip-guide-card-bottom-border p-3" :class="showGuide ? 'd-sm-block d-md-none d-lg-none d-xl-none d-xxl-none' : 'd-none' ">
                            <GuideComponent
                                :content="guideContent"
                                @triggerGuideAction="triggerGuideAction"
                                :customizedTitle="true"
                                :title-classes="'fs-6 nip-help-grey-text mt-5 mb-5'"
                            />
                        </div>
                        <div class="p-4" :class="showGuide ? 'col-md-8' : 'col-12'">
                            <div class="p-3">
                                <span role="button" id="shareholder_information_show_guide_button" @click="triggerGuideAction()" v-show="!showGuide">
                                    <i class="fa-solid fa-circle-question float-end fa-lg nip-question-mark-helper"></i>
                                </span>
                                <p class="fw-semibold fs-4 card-title mb-4"> Shareholders Information </p>
                                <span class="fw-semibold fs-5">{{ isCorporateOfficer ? 'Corporate' : 'Person'}}</span>
                                <div class="row mb-3 mt-1">
                                    <div class="col-md-7">
                                        <template v-if="isCorporateOfficer">
                                            <div class="row mb-1">
                                                <FormLabel
                                                    forId="shareholder-company-name"
                                                    labelText="Company Name"
                                                />
                                                <div class="col-md-6">
                                                    <input class="form-control nip-disabled-input-bg" type="text" disabled v-model="officerData.companyName">
                                                </div>
                                            </div>
                                            <div class="row mb-1">
                                                <FormLabel
                                                    forId="shareholder-first-name"
                                                    labelText="First name"
                                                />
                                                <div class="col-md-6">
                                                    <input class="form-control nip-disabled-input-bg" type="text" disabled v-model="officerData.firstname">
                                                </div>
                                            </div>
                                            <div class="row mb-1">
                                                <FormLabel
                                                    forId="shareholder-first-name"
                                                    labelText="Last name"
                                                />
                                                <div class="col-md-6">
                                                    <input class="form-control nip-disabled-input-bg" type="text" disabled v-model="officerData.lastname">
                                                </div>
                                            </div>
                                        </template>
                                        <template v-else class="col-md-5">
                                            <div class="row mb-2">
                                                <FormLabel
                                                    forId="shareholder-full-name"
                                                    labelText="Full Name"
                                                />
                                                <div class="col-md-6">
                                                <input class="form-control nip-disabled-input-bg" type="text" disabled v-model="officerData.fullName">
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <hr/>
                                <div id="allotment-of-shares">
                                    <AllotmentOfShares
                                        v-for="(share, index) in shares"
                                        v-bind:key="index"
                                        :allotment-of-shares="share"
                                        :index="index"
                                        :is-auto-filled="shareholderData.isAutoFill"
                                        :currencies="shareholderData.currencies"
                                        :available-shares="shareholderData.availableShares"
                                        @update:allotmentOfShares="updateAllotmentOfShares"
                                        @removeShares="removeShares"
                                        :errors="allotmentOfSharesErrors[index] ?? {}"
                                    />
                                </div>
                                <div class="row mb-3 mt-1">
                                    <div class="col-md-7">
                                        <div class="row mb-2">
                                            <div class="col-sm-6"></div>
                                            <div class="col-md-6">
                                                <button class="btn nip-link" id="addSharesButton" @click="addShares()">Add another share class</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert nip-light-sky-blue-bg mt-4" role="alert">
                                    <p class="text-dark">
                                        <span class="fw-bold">Ordinary shares</span>
                                        and the associated <span class="fw-bold">voting rights</span> are suitable for most companies. We recommend not changing them unless you have received professional
                                        advice.
                                    </p>
                                </div>
                                <hr/>
                                <div id="security-questions" class="row">
                                    <div class="col-md-7">
                                        <SecurityQuestions
                                            :security-questions="{
                                                telephone: telephone,
                                                birthTown: birthTown,
                                                mumsMaidenName: mumsMaidenName
                                            }"
                                            :authentication-suggestion="authenticationSuggestion"
                                            @update:securityQuestions="updateSecurityQuestion"
                                            :errors="securityQuestionsErrors"
                                        />
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="alert alert-danger" v-if="errorMessage">
                                            <p v-for="error in formattedErrorMessage" :key="error">{{ error }}</p>
                                        </div>
                                        <div class="flex-align-end">
                                            <div class="col-12 col-md-6 col-lg-3">
                                                <button class="btn nip-btn-orange nip-next-step-button float-end d-flex align-items-center" @click="saveInfo()" id="shareholder_information_continue_button_85860180">
                                                    Submit & Continue
                                                    <span class="arrow">
                                                        <span class="arrow-shaft arrow-shaft-color-override-white"></span>
                                                        <span class="arrow-head arrow-head-color-override-white"></span>
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white m-0 nip-guide-card-border p-3" :class="showGuide ? 'd-lg-block d-xl-block d-md-block d-xxl-block d-none col-md-4' : 'd-none' ">
                            <GuideComponent
                                :content="guideContent"
                                @triggerGuideAction="triggerGuideAction"
                                :customizedTitle="true"
                                :title-classes="'fs-6 nip-help-grey-text mt-5 mb-5'"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import api from "@/modules/CompanyIncorporation/Api"
import ShareholderSummary from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/Shareholder/ShareholderSummary.vue"
import GuideComponent from "@/modules/CompanyIncorporation/components/global/GuideComponent.vue"
import AllotmentOfShares from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/Shareholder/AllotmentOfShares.vue"
import SecurityQuestions from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/Shareholder/SecurityQuestions.vue"
import FormLabel from "@/modules/CompanyIncorporation/components/global/FormLabel.vue";
import { STRING_TYPE } from "@/modules/CompanyIncorporation/constants/variableTypes"
import { SecurityQuestionsValidator } from "@/modules/CompanyIncorporation/validators/securityQuestionsValidator";
import { PSC_OFFICER } from "@/modules/CompanyIncorporation/constants/officerTypes";
import {createLog} from "@/modules/CompanyIncorporation/listeners/logEventListener";
import {
    COMPANY_INCORPORATION_APPOINTMENTS_MEMBER,
    SHAREHOLDER_INFORMATION_EDIT,
    SHAREHOLDER_INFORMATION_LOAD,
    SHAREHOLDER_INFORMATION_SUBMIT
} from "@/modules/CompanyIncorporation/constants/stepEnums";
import {AllotmentOfSharesValidator} from "@/modules/CompanyIncorporation/validators/allotmentOfSharesValidator";

export default  defineComponent({
    components: {
        ShareholderSummary,
        GuideComponent,
        AllotmentOfShares,
        SecurityQuestions,
        FormLabel
    },
    data() {
        return {
            isLoading: false,
            isEditing: false,
            doneEditing: false,
            showGuide: false,
            guideContent: `
                <p class="nip-help-grey-text lh-base">A subscriber (shareholder) or member can be a company director or secretary or a new appointment.</p>
                <p class="nip-help-grey-text lh-base">Shareholders and shareholding determine the ownership of a company.</p>
                <p class="nip-help-grey-text lh-base">For companies limited by guarantee the owners of the company are called Members or Guarantors.</p>
            `,
            birthTown: '',
            mumsMaidenName: '',
            telephone: '',
            errorMessage: null,
            currentSubStepTitle: 'Shareholder Information',
            currentSubStep: '',
            officerRoles: [] as string[],
            officerData: {
                firstname: this.shareholderData?.firstname ?? '',
                lastname: this.shareholderData?.lastname ?? '',
                companyName: this.shareholderData?.companyName ?? '',
                fullName: this.shareholderData?.fullName ?? '',
            },
            securityQuestionsErrors: {},
            allotmentOfSharesErrors: {},
            shares: [],
            defaultVotingRight: 'Ordinary shares have full rights in the company with respect to voting, dividends and distributions.'
        }
    },
    props: {
        companyId: {
            type: Number,
            required: true
        },
        isCorporateOfficer: {
            type: Boolean,
            required: true
        },
        consolidatedMemberHash: {
            type: String,
            required: false
        },
        shareholderData: {
            type: Object,
            required: true
        },
        subStepsData: {
            type: Object,
            required: true
        },
        subStepTitle: {
            type: String,
            required: true
        },
        roles: {
            required: true
        },
        authenticationSuggestion: {
            type: Object,
            required: false,
            default: () => null
        },
        details: {
            type: Object,
            required: true,
        },
        stepUrl: {
            type: String,
            required: true,
        },
    },
    emits: ['shareholderSaved'],
    methods: {
        triggerGuideAction() {
            this.showGuide = !this.showGuide;
        },
        async saveInfo() {
            this.errorMessage = null;
            this.securityQuestionsErrors = {};
            this.allotmentOfSharesErrors = {};
            try {
                await createLog(this.companyId, {
                    newIncorporationProcess: true,
                    step: COMPANY_INCORPORATION_APPOINTMENTS_MEMBER,
                    subStep: SHAREHOLDER_INFORMATION_SUBMIT,
                    data: JSON.stringify({...this.$data, ...this.$props}),
                });
            } catch (e) {
                console.error(e)
            }
            try {

                if (!this.validateShares()) {
                  return
                }

                const securityValidator = new SecurityQuestionsValidator(this.mumsMaidenName, this.telephone, this.birthTown)
                if (!securityValidator.validate()) {
                    this.securityQuestionsErrors = securityValidator.errors;
                    return
                }

                const requestBody = new FormData();
                requestBody.append('shareholderData', JSON.stringify({
                    authentication: {
                        birthTown: this.birthTown,
                        mumsMaidenName: this.mumsMaidenName,
                        telephone: this.telephone
                    },
                    allotmentOfShares: this.shares
                }));
                requestBody.append('companyId', JSON.stringify(this.companyId));
                requestBody.append('memberHash', this.consolidatedMemberHash);

                document.getElementById('shareholder_information_continue_button_85860180').disabled = true
                api.post(`/api/incorporation/appointments/save_shareholder_info/`, requestBody)
                .then((response) => {
                    this.doneEditing = true;
                    this.isEditing = false;
                    this.shareholderData.isAutoFill = false;
                    this.currentSubStep = '';

                    this.consolidatedMemberHash = response.data.membersHash;
                    const nextSubstep = this.definedNextSubStep();
                    this.$emit('shareholderSaved', response.data, nextSubstep)

                    let memberType = this.isCorporateOfficer ? 'corporate' : 'person'
                    window.history.replaceState({}, '', `${this.stepUrl}${memberType}/member/${this.consolidatedMemberHash}/`);

                    this.markSharesAsCompleted()
                    this.updateShareholderData()

                    if (nextSubstep.name === 'summary') {
                        return window.location.href = `${window.location.origin}/incorporation/${this.companyId}/appointments/`
                    }
                })
                .catch ((error: any) => {
                    if (error.response && error.response.data && error.response.data.error) {
                        this.errorMessage = error.response.data.error.split(',');
                        return;
                    }
                    this.errorMessage = error.message;
                    document.getElementById('shareholder_information_continue_button_85860180').disabled = false
                })
                
            } catch(e: unknown) {
                console.error(e)
                this.errorMessage = 'Error saving your shareholder information, please try again'
            }
        },
        updateAllotmentOfShares(data: any, index: Number) {
            this.shares[index].currency = data.currency;
            this.shares[index].shareClass = data.shareClass;
            this.shares[index].numberShare = data.numberShare;
            this.shares[index].shareValue = data.shareValue;
            this.shares[index].votingRight = data.votingRights;
        },
        updateSecurityQuestion(data: any) {
            this.mumsMaidenName = data.mumsMaidenName;
            this.telephone = data.telephone;
            this.birthTown = data.birthTown;
        },
        shareholderSubStepCompletedListener() {
            document.addEventListener('shareholderSubStepCompleted', (_event: CustomEvent) => {});
        },
        shareholderStepListener() {
            document.addEventListener('showShareholderSubStep', (event: CustomEvent) => {
                if (event.detail?.isCorporate) {
                    this.officerData.firstname = this.details.corporateName.forename;
                    this.officerData.lastname = this.details.corporateName.surname;
                    this.officerData.companyName = this.details.corporateName.companyName;
                } else {
                    this.officerData.fullName = event.detail.fullName;
                }

                if (event.detail?.roles) {
                    this.officerRoles = event.detail.roles
                }

                this.isEditing = true;
                this.currentStep = this.currentSubStepTitle;
            });
        },
        async handleEditing() {
            this.currentSubStep = this.currentSubStepTitle;
            this.isEditing = true;
            this.doneEditing = false;

            await createLog(this.companyId, {
                newIncorporationProcess: true,
                step: COMPANY_INCORPORATION_APPOINTMENTS_MEMBER,
                subStep: SHAREHOLDER_INFORMATION_EDIT,
                data: JSON.stringify({...this.$data, ...this.$props}),
            });
        },
        definedNextSubStep() {
            const hasRole = (role: string) => this.selectedRoles.includes(role);

            if (hasRole(PSC_OFFICER)) {
                return {
                    label: 'Person with Significant Control',
                    name: 'psc'
                };
            }

            return {
                label: 'Appointments Summary',
                name: 'summary'
            };
        },
        updateShareholderData(){
            this.shareholderData.shares = this.shares
        },
        markSharesAsCompleted() {
            this.shares.forEach((share) => {
                share.completed = true
            });
        },
        validateShares() {
            this.allotmentOfSharesErrors = {}

            let isValid = true;
            for (const [index, share] of this.shares.entries()) {
              const sharesValidator = new AllotmentOfSharesValidator(share)
              if (!sharesValidator.validate()) {
                this.allotmentOfSharesErrors[index] = sharesValidator.errors;
                isValid = false;
              }
            }

            return isValid;
        },
        loadShare() {
            this.shares = this.shareholderData.shares
        },
        addShares() {
            this.shares.push({
                shareClass: 'Ordinary',
                numberShare: 1,
                currency: 'GBP £',
                prescribedParticulars: 'Ordinary shares have full rights in the company with respect to voting, dividends and distributions.',
                shareValue: '1',
                defaultVotingRights: 'Ordinary shares have full rights in the company with respect to voting, dividends and distributions.',
                votingRight: 'Ordinary shares have full rights in the company with respect to voting, dividends and distributions.',
                allShareVotingRights: '',
                completed: false
            })
        },
        removeShares(index) {
            this.shares.splice(index, 1)
        },
    },
    computed: {
        isLoadingData(): boolean {
            return this.isLoading;
        },
        parsedSecurityQuestion() {
            const authentication = this.shareholderData.authentication;
            if (typeof authentication === STRING_TYPE ) {
                return JSON.parse(authentication);
            }
            return authentication;
        },
        isCurrentAddressSubStep(): boolean {
            return this.currentSubStep === this.currentSubStepTitle;
        },
        showSummary(): boolean {
            return !this.isCurrentAddressSubStep && !this.isEditing;
        },
        hasSecurityQuestions(): boolean {
            return this.mumsMaidenName !== null && this.birthTown !== '' && this.telephone !== '';
        },
        formattedErrorMessage() {
            return Array.isArray(this.errorMessage)
                ? this.errorMessage
                : [this.errorMessage];
        },
        nextSubStepLabel(): string {
            const data = this.definedNextSubStep();
            return data.label;
        },
        selectedRoles(): string[] {
            const parsedRoles = typeof this.roles === STRING_TYPE ? JSON.parse(this.roles) : this.roles
            const roleIds = parsedRoles.filter((role: any) => role.checked).map((role: any) => role.id)

            return [...this.officerRoles, ...roleIds];
        },
    },
    watch: {
        shareholderData: {
            immediate: true,
            handler(newValue) {
                this.officerData.firstname = newValue.firstname;
                this.officerData.lastname = newValue.lastname;
                this.officerData.companyName = newValue.companyName;
                this.officerData.fullName = newValue.fullName;
            },
            deep: true
        },
        parsedSecurityQuestion: {
            immediate: true,
            handler(newValue: any) {
                if (newValue) {
                    this.telephone = newValue.telephone;
                    this.mumsMaidenName = newValue.mumsMaidenName;
                    this.birthTown = newValue.birthTown
                }
            }
        },
        subStepTitle: {
            immediate: true,
            handler(newValue: any) {
                if (newValue) {
                    this.currentSubStep = newValue
                }
            }

        }
    },
    async mounted() {
        this.shareholderStepListener();
        this.loadShare();

        await createLog(this.companyId, {
            newIncorporationProcess: true,
            step: COMPANY_INCORPORATION_APPOINTMENTS_MEMBER,
            subStep: SHAREHOLDER_INFORMATION_LOAD,
            data: JSON.stringify({...this.$data, ...this.$props}),
        });
    },
    beforeUnmount() {
        document.removeEventListener('showShareholderSubStep', () => {});
    }
})
</script>