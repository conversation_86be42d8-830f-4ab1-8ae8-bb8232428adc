<template>
    <div>
        <div>
            <ChangeCompanyName
                :companyName="parsedCompany.name"
                :companyStatus="parsedCompany.status ?? ''"
                :url="changeNameUrl"
                :company-type="parsedCompany.category"
            />
            <IncorporationSteps
                :steps="parsedTabSteps"
                :currentStep="serializedCurrentStep[1].title"
            />
            <p class="fw-semibold fs-6 mt-2 text-decoration-underline">
                <a class="link-dark" :href="serializedCurrentStep[0].link" id="back_to_previous_step_link_65940461">
                    <i class="fa fa-long-arrow-left me-2"></i>
                    Back to {{ serializedCurrentStep[0].title }}
                </a>
            </p>
            <div class="d-flex justify-content-between mt-4 mb-4">
                <div class="fw-semibold fs-3">Company Appointments</div>
                <a
                    v-if="!isOfficerComplete && companyHasMembers"
                    class="nip-link mt-2"
                    data-bs-toggle="modal"
                    data-bs-target="#delete-modal"
                    id="dismiss-officer-modal-link"
                >
                    Dismiss Appointment
                </a>
            </div>
            <div class="alert alert-danger" v-if="errorMessage">
                {{ errorMessage }}
            </div>
        </div>
        <div>
            <div id="appointment-general-info">
                <GeneralInformation
                    :company="company"
                    :company-id="companyId"
                    :countries-of-residence="personCountriesOfResidence"
                    :nationalities="nationalities"
                    :details="parsedDetails"
                    :customer-details="parsedCustomerDetails"
                    :has-other-companies="hasOtherCompanies"
                    :dob="dob"
                    :is-corporate="isCorporate"
                    :roles="roles"
                    :consent-to-act="consentToAct"
                    :consolidated-member-hash="consolidatedMemberHash"
                    :add-person-url="addPersonUrl"
                    :add-corporate-url="addCorporateUrl"
                    :titles="titles"
                    :step-url="backUrl"
                    :add-member-api-url="addMemberApiUrl"
                    :suggested-nature-of-control="suggestedNatureOfControl"
                    :psc-warning="pscWarning"
                    @roleSelectionProcess="roleSelectionProcess"
                    @memberSaved="memberSaved"
                    :sub-step-title="currentSubStepTitle"
                    @nextSubStepHandler="nextSubStepHandler"
                ></GeneralInformation>
            </div>

            <div id="appointment-member-details" v-if="isSubStepAvailable('memberDetails') && showMemberDetails">
                <MemberDetails
                    :company="company"
                    :company-id="companyId"
                    :details="details"
                    :dob="dob"
                    :is-corporate="isCorporate"
                    :roles="roles"
                    :consent-to-act="consentToAct"
                    :authentication="parsedAuthentication"
                    :consolidated-member-hash="consolidatedMemberHash"
                    :sub-step-title="currentSubStepTitle"
                    @nextSubStepHandler="nextSubStepHandler"
                ></MemberDetails>
            </div>

            <div id="appointment-addresses" v-if="isSubStepAvailable('addresses')">
                <Addresses
                    :sub-steps-data="parsedSubSteps"
                    :residential-address-countries="residentialAddressCountries"
                    :service-address-countries="serviceAddressCountries"
                    :is-corporate="isCorporate"
                    :service-address="parsedServiceAddress"
                    :residential-address="parsedResidentialAddress"
                    :has-address-data="hasAddressData"
                    :roles="roles"
                    :company="parsedCompany"
                    :sub-step-title="currentSubStepTitle"
                    @nextSubStepHandler="nextSubStepHandler"
                    @addressSaved="addressSaved"
                    :officer-country-of-residence="parsedDetails.countryOfResidence ?? ''"
                    :is-using-msg-address="isUsingMsgAddress"
                />
            </div>

            <div id="appointment-member-info" v-if="isSubStepAvailable('member') && showMemberInformation">
                <MemberInformation
                    :company="company"
                    :company-id="companyId"
                    :details="details"
                    :is-corporate="isCorporate"
                    :consent-to-act="consentToAct"
                    :authentication="parsedAuthentication"
                    :consolidated-member-hash="consolidatedMemberHash"
                    :roles="roles"
                    :sub-step-title="currentSubStepTitle"
                    @nextSubStepHandler="nextSubStepHandler"
                ></MemberInformation>
            </div>

            <div id="appointment-shareholder" v-if="isSubStepAvailable('shareholder') && shareholderInfoSelected">
                <ShareholderInformation
                    :shareholder-data="shareholderData"
                    :authentication-suggestion="parsedAuthenticationSuggestion"
                    :company-id="companyId"
                    :is-corporate-officer="isCorporate"
                    :consolidated-member-hash="consolidatedMemberHash"
                    :sub-steps-data="parsedSubSteps"
                    :sub-step-title="currentSubStepTitle"
                    :roles="roles"
                    :details="parsedDetails"
                    :step-url="backUrl"
                    @shareholderSaved="shareholderSaved"
                />
            </div>

            <div id="appointment-psc" v-if="isSubStepAvailable('psc') && showPSC">
                <PeopleWithSignificantControl
                    :company="company"
                    :company-id="companyId"
                    :details="parsedDetails"
                    :dob="parsedDob"
                    :is-corporate="isCorporate"
                    :consolidated-member-hash="consolidatedMemberHash"
                    :nature-of-control="natureOfControl"
                    :nature-of-control-options="natureOfControlOptions"
                    :suggested-nature-of-control="suggestedNatureOfControl"
                    :service-address="parsedServiceAddress"
                    :sub-step-title="currentSubStepTitle"
                    :company-has-shareholders="companyHasShareholders"
                ></PeopleWithSignificantControl>
            </div>
        </div>

        <div
            class="modal fade"
            id="delete-modal"
            data-bs-backdrop="static"
            data-bs-keyboard="false"
            tabindex="-1"
            aria-hidden="true"
            style="z-index: 100000;"
        >
            <div class="modal-dialog modal-dialog-centered custom-modal-width">
                <div class="modal-content p-4" style="border-radius: 0 !important;">
                    <div class="modal-header">
                        <h5 class="modal-title fw-semibold nip-text-color">Dismiss Appointment</h5>
                        <button type="button"
                                id="close-dismiss-appointment-modal-button"
                                class="btn-close nip-close-modal-icon mr-4"
                                data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>

                    <div class="modal-body">
                        <p>
                            Are you sure you want to delete this appointment?
                        </p>
                    </div>

                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div>
                            <a id="close-dismiss-appointment-mmodal-link"
                                class="nip-link-light fw-semibold"
                                data-bs-dismiss="modal">Close</a>
                        </div>
                        <div class="d-flex flex-align-end">
                            <button type="button"
                                    id="dismiss-appointment-button"
                                    class="btn btn-outline-orange nip-next-step-button btn-view-formation w-100"
                                    @click="deleteMember">
                                <span class="fs-6">Delete</span> <i class="fa fa-long-arrow-right mx-3"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>


</template>

<script lang="ts">
    import { defineComponent } from "vue";
    import Header from "@/modules/CompanyIncorporation/components/global/Header.vue";
    import Addresses from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/Addresses/Addresses.vue";
    import GeneralInformation
        from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/GeneralInformation.vue";
    import PeopleWithSignificantControl
        from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/PeopleWithSignificantControl.vue";
    import MemberInformation
        from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/MemberInformation.vue";
    import ShareholderInformation
        from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/Shareholder/ShareholderInformation.vue";
    import MemberDetails
        from "@/modules/CompanyIncorporation/components/AppointmentsStep/subSteps/MemberDetails.vue";
    import GuideComponent from "@/modules/CompanyIncorporation/components/global/GuideComponent.vue";
    import { SUBSCRIBER_OFFICER, DIRECTOR_OFFICER, PSC_OFFICER } from "@/modules/CompanyIncorporation/constants/officerTypes";
    import { BYSHARE, LLP, BYGUAR } from "@/modules/CompanyIncorporation/constants/companyCategories";
    import { STRING_TYPE } from "../../constants/variableTypes";
    import IncorporationSteps from "@/modules/CompanyIncorporation/components/IncorporationSteps.vue";
    import ChangeCompanyName from "@/modules/CompanyIncorporation/components/ChangeCompanyName.vue";
    import api from "./../../Api";

    export default defineComponent({
        data() {
            return {
                currentStep: "appointments",
                showMemberDetails: false,
                showMemberInformation: false,
                shareholderInfoSelected: false,
                showPSC: false,
                generalInfoTitle: 'General Information',
                memberDetailsTitle: 'Member Details',
                memberInfoTitle: 'Member Information',
                shareholderTitle: 'Shareholder Information',
                pscTitle: 'People with Significant Control',
                addressTitle: 'Addresses',
                currentSubStepTitle: 'General Information',
                displayedSubSteps: {
                    'General Information': true,
                    'Addresses': true,
                    'Member Details': false,
                    'Member Information': false,
                    'Shareholder Information': false,
                    'People with Significant Control': false,
                },
                step: null,
                showHeader: false,
                errorMessage: null,
            };
        },
        components: {
            ChangeCompanyName, IncorporationSteps,
            GuideComponent,
            Header,
            Addresses,
            GeneralInformation,
            PeopleWithSignificantControl,
            MemberInformation,
            ShareholderInformation,
            MemberDetails,
        },
        props: {
            company: {
                type: String,
                required: true
            },
            tabSteps: {
                type: String,
                required: true
            },
            changeNameUrl: {
                type: String,
                required: true
            },
            subStepsData: {
                type: String,
                required: true
            },
            serializedCurrentStep: {
                type: Array,
                required: true
            },
            backUrl: {
                type: String,
                required: true
            },
            residentialAddressCountries: {
                type: Object,
                required: true
            },
            personCountriesOfResidence: {
                type: Object,
                required: true
            },
            serviceAddressCountries: {
                type: Object,
                required: true
            },
            nationalities: {
                type: String,
                required: true
            },
            details: {
                type: String,
                required: true
            },
            customerDetails: {
                type: String,
                required: true
            },
            hasOtherCompanies: {
                type: Boolean,
                required: true
            },
            dob: {
                type: String,
                required: true
            },
            isCorporate: {
                type: Boolean,
                required: true
            },
            roles: {
                type: String,
                required: true
            },
            consentToAct: {
                type: Boolean,
                required: true
            },
            consolidatedMemberHash: {
                type: String,
                required: false
            },
            addPersonUrl: {
                type: String,
                required: true
            },
            addCorporateUrl: {
                type: String,
                required: true
            },
            addMemberApiUrl: {
                type: String,
                required: true
            },
            titles: {
                type: String,
                required: true
            },
            authentication: {
                type: String,
                required: true
            },
            authenticationSuggestion: {
                type: String,
                required: false,
                default: null
            },
            saveMemberDetailsApiUrl: {
                type: String,
                required: true
            },
            hasAddressData: {
                type: Boolean,
                required: true
            },
            serviceAddress: {
                type: String
            },
            residentialAddress: {
                type: String
            },
            shareholderData: {
                type: Object,
                required: true
            },
            natureOfControl: {
                type: String,
                required: true
            },
            natureOfControlOptions: {
                type: String,
                required: true
            },
            suggestedNatureOfControl: {
              type: String,
              required: false,
              default: null
            },
            availableSubSteps: {
                type: String,
                required: true
            },
            isUsingMsgAddress: {
                type: Boolean,
                required: true
            },
            companyHasShareholders: {
                type: Boolean,
                required: true
            },
            companyHasMembers: {
                type: Boolean,
                required: true
            },
            isOfficerComplete: {
                type: Boolean,
                required: true,
                default: false
            },
            pscWarning: {
              type: String,
              required: false,
              default: null
            },
        },
        methods: {
            roleSelectionProcess(checked?: boolean, role?: string) {
                if (!role && !checked) {
                    const parsedRoles = JSON.parse(this.roles);
                    this.shareholderInfoSelected = parsedRoles.find(r => r.id === SUBSCRIBER_OFFICER)?.checked && this.isCompanyByShare();
                    this.showMemberDetails = parsedRoles.find(r => r.id === DIRECTOR_OFFICER)?.checked && this.isCompanyLLP();
                    this.showMemberInformation = parsedRoles.find(r => r.id === SUBSCRIBER_OFFICER)?.checked && this.isCompanyByGuar();
                    this.showPSC = parsedRoles.find(r => r.id === PSC_OFFICER)?.checked

                    return;
                }

                if (role === DIRECTOR_OFFICER) {
                    this.showMemberDetails = checked && this.isCompanyLLP()
                }

                if (role === SUBSCRIBER_OFFICER) {
                    this.shareholderInfoSelected = checked && this.isCompanyByShare();
                    this.showMemberInformation = checked && this.isCompanyByGuar()
                }

                if (role === PSC_OFFICER) {
                    this.showPSC = checked;
                }

            },
            isCompanyByGuar():boolean {
                return this.parsedCompany.category === BYGUAR;
            },
            isCompanyLLP():boolean {
                return this.parsedCompany.category === LLP;
            },
            isCompanyByShare():boolean {
                return this.parsedCompany.category === BYSHARE;
            },
            memberSaved(data) {
                this.consolidatedMemberHash = data.membersHash;
                this.authentication = JSON.stringify(data.authentication);
                this.details = JSON.stringify(data.details);
                this.dob = JSON.stringify(data.dob);
                this.roles = JSON.stringify(data.roles);
                this.natureOfControl = JSON.stringify(data.natureOfControl);
                this.isOfficerComplete = data.isOfficerComplete;

                this.companyHasMembers = true;
                this.updateHeader();

                if (this.isSubStepAvailable('general')) {
                    this.currentSubStepTitle = this.addressTitle;
                    return;
                }

            },
            shareholderSaved(data, nextSubStep) {
                this.consolidatedMemberHash = data.membersHash;

                this.$nextTick(() => {
                    this.nextSubStepHandler(nextSubStep);
                });
            },
            addressSaved(data) {
                this.serviceAddress = JSON.stringify(data.serviceAddress)
                this.residentialAddress = JSON.stringify(data.residentialAddress)
                this.isUsingMsgAddress = data.isUsingMsgAddress
                this.isOfficerComplete = data.isOfficerComplete
            },
            nextSubStepHandler(subStep: object) {
                if (this.isOfficerComplete) {
                    return window.location.href = `/incorporation/${this.companyId}/appointments/`
                }

                if (subStep.name === 'shareholder' && this.isSubStepAvailable('shareholder')) {
                    const data = {
                        isCorporate: this.isCorporate,
                        ...(this.isCorporate)
                            ? {
                                companyName: this.parsedDetails.companyName,
                                firstname: this.parsedDetails.forename,
                                lastname: this.parsedDetails.surname
                            }
                            : {
                                fullName: `${this.parsedDetails.forename} ${this.parsedDetails.surname}`
                            }
                    }
                    const event = new CustomEvent('showShareholderSubStep', { detail: data });
                    this.currentSubStepTitle = this.shareholderTitle
                    document.dispatchEvent(event);
                    return;
                }

                if (subStep.name === 'memberDetails' && this.isSubStepAvailable('memberDetails')) {
                    this.currentSubStepTitle = this.memberDetailsTitle;
                    document.dispatchEvent(new CustomEvent('showMemberDetailsSubStep',{
                            detail: {}
                        })
                    );
                    return;
                }

                if (subStep.name === 'member' && this.isSubStepAvailable('member')) {
                    this.currentSubStepTitle = this.memberInfoTitle;
                    document.dispatchEvent(new CustomEvent('showMemberInfoSubStep',{
                            detail: {}
                        })
                    );
                    return;
                }

                if (subStep.name === 'psc' && this.isSubStepAvailable('psc')) {
                    this.currentSubStepTitle = this.pscTitle;
                    document.dispatchEvent(new CustomEvent('showPscSubStep',{
                            detail: null
                        })
                    );
                    return;
                }
            },
            isSubStepAvailable(subStepName: string) {
                return this.parsedAvailableSubSteps.includes(subStepName);
            },
            prepareDisplayedSubSteps() {
                this.displayedSubSteps['Member Details'] = this.isSubStepAvailable('memberDetails') && this.showMemberDetails;
                this.displayedSubSteps['Shareholder Information'] = this.isSubStepAvailable('shareholder') && this.shareholderInfoSelected;
                this.displayedSubSteps['Member Information'] = this.isSubStepAvailable('member') && this.showMemberInformation;
                this.displayedSubSteps['People with Significant Control'] = this.isSubStepAvailable('psc') && this.showPSC;
            },
            getLastSubStep() {
                return Object.keys(this.displayedSubSteps).filter(key => this.displayedSubSteps[key] === true).pop();
            },
            updateHeader() {
                this.showHeader = false;

                this.step = [...this.serializedCurrentStep];

                if (this.isOfficerComplete || (this.companyHasMembers && !this.consolidatedMemberHash)) {
                    this.step[0] = {...this.step[1]};
                    this.step[0].title = 'Appointments Summary';
                }

                this.showHeader = true;
            },
            deleteMember() {
                if (this.consolidatedMemberHash == null || this.consolidatedMemberHash == '') {
                    return window.location.href = `/incorporation/${this.companyId}/appointments/`
                }
                api.post(`/api/incorporation/${this.companyId}/appointments/delete_member/${this.consolidatedMemberHash}/`)
                    .then((response) => {
                        return window.location.href = `/incorporation/${this.companyId}/appointments/`
                    })
                    .catch(error => {
                        this.handleError(error);
                    })
                    .finally(() =>{
                        this.closeModal()
                    });
            },
            handleError(error: any) {
                if (error.response && error.response.data && error.response.data.error) {
                    this.errorMessage = error.response.data.error;
                } else {
                    this.errorMessage = error.message || "An unknown error occurred.";
                }
            },
            closeModal() {
                var myModalEl = document.getElementById('delete-modal');
                var modal = bootstrap.Modal.getInstance(myModalEl)
                modal.hide();
            }
        },
        computed: {
            parsedCompany() {
                return JSON.parse(this.company);
            },
            parsedTabSteps() {
                return JSON.parse(this.tabSteps);
            },
            parsedSubSteps() {
                return JSON.parse(this.subStepsData);
            },
            companyId() {
                return this.parsedCompany.id;
            },
            parsedServiceAddress() {
                try {
                    return JSON.parse(this.serviceAddress) ?? {};
                } catch (error) {
                    return {};
                }
            },
            parsedResidentialAddress() {
                try {
                    return JSON.parse(this.residentialAddress) ?? {};
                } catch (error) {
                    return {};
                }
            },
            parsedDetails() {
                return typeof this.details === STRING_TYPE ? JSON.parse(this.details) : this.details;
            },
            parsedCustomerDetails() {
                try {
                    return JSON.parse(this.customerDetails);
                } catch {
                    return {};
                }
            },
            parsedDob() {
                try {
                    return typeof this.dob === STRING_TYPE ? JSON.parse(this.dob) : this.dob;
                } catch (error) {
                    return null;
                }
            },
            isNatureOfControlEmpty(): boolean {
                return !(this.natureOfControl?.ownershipOfShares
                    || this.natureOfControl?.ownershipOfVotingRights
                    || this.natureOfControl?.rightToAppointAndRemoveDirectors
                    || this.natureOfControl?.significantInfluenceOrControl)
            },
            parsedAuthentication() {
                try {
                    return JSON.parse(this.authentication);
                } catch {
                    return null;
                }
            },
            parsedAvailableSubSteps() {
                return JSON.parse(this.availableSubSteps);
            },
            parsedNatureOfControl() {
                try {
                    return typeof this.natureOfControl === STRING_TYPE ? JSON.parse(this.natureOfControl) : this.natureOfControl;
                } catch {
                    return null;
                }
            },
            parsedAuthenticationSuggestion() {
                try {
                    return JSON.parse(this.authenticationSuggestion);
                } catch {
                    return {};
                }
            },
        },
        mounted() {
            this.updateHeader();
            this.prepareDisplayedSubSteps();
        },
        created() {
            this.roleSelectionProcess();
        },
    });
</script>