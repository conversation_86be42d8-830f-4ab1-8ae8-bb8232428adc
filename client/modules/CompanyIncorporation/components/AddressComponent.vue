<template>
  <div class="content">
  <template v-if="shouldRenderAddress">

<!--    <div class="col-sm-12 bg-light px-4 pt-3 border-end border-start border-top" id="fieldset_1">-->
<!--      <p class="fw-semibold fs-5 mb-4 mt-3">Prefill Address</p>-->
<!--      <div class="row mb-3">-->
<!--        <div class="col-sm-6">-->
<!--          <label for="prefill">Prefill</label>-->
<!--        </div>-->
<!--        <div class="col-sm-5 col-11">-->

<!--          <select :id="formId ? `${formId}[prefill]` : 'prefill'" :name="formId ? `${formId}[prefill]` : 'prefill'"-->
<!--                  class="form-select" @change="fillAddress($event)">-->
<!--            <option value="?">Select</option>-->
<!--            <option v-for="(item, index) in prefill.select" :value="index" :key="index">{{ item }}</option>-->
<!--          </select>-->

<!--        </div>-->
<!--        <div class="col-1">-->
<!--          <i-->
<!--            class="fas fa-question-circle text-cms-blue text-decoration-none fs-5 float-end"-->
<!--            data-bs-toggle="tooltip"-->
<!--            data-bs-placement="bottom"-->
<!--            data-bs-custom-class="custom-tooltip"-->
<!--            title="<em>You can use this prefill option to select an address you&#39;ve entered previously.</em>"-->
<!--            data-bs-html="true"-->
<!--          >-->
<!--          </i>-->
<!--        </div>-->
<!--        <div class="col-12 has-error text-danger">-->
<!--          <div class="help-block"></div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->

    <div class="col-sm-8" id="fieldset_2">

<!--      <p class="small mb-4">Fields with a * are mandatory.</p>-->
      <div class="row mb-3">
        <div class="col-md-4">
          <label for="premise" class="nip-text-color">Building name/number *</label>
        </div>
        <div class="col-md-8 d-flex align-items-center position-relative">
          <input
              type="text"
              :value="address?.premise"
              :id="formId ? `${formId}[premise]` : 'premise'"
              :name="formId ? `${formId}[premise]` : 'premise'"
              class="form-control me-2"
              ref="premise_address">
            <Tooltip
                :content="'Eg. &quot;28A&quot; or &quot;Flat 2, 36&quot; or &quot;Crusader House&quot;'"
                :tooltip-icon-classes="'fas fa-info-circle nip-info-tooltip-color fs-5 float-end'"
            />
        </div>
        <div class="col-12 has-error text-danger">
          <div v-for="error in fieldErrors?.premise" class="help-block">{{ error }}</div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <label for="street" class="nip-text-color">Street *</label>
        </div>
        <div class="col-md-8 d-flex align-items-center position-relative">
          <input type="text" :value="address?.street"
                 :id="formId ? `${formId}[street]` : 'street'"
                 :name="formId ? `${formId}[street]` : 'street'"
                 class="form-control me-2"
                 ref="street_address">
            <Tooltip
                :content="'Street refers to the actual street, road, lane etc.'"
                :tooltip-icon-classes="'fas fa-info-circle nip-info-tooltip-color fs-5 float-end'"
            />
        </div>
        <div class="col-12 has-error text-danger">
          <div v-for="error in fieldErrors?.street" class="help-block">{{ error }}</div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <label for="thoroughfare" class="nip-text-color">Address 3</label>
        </div>
        <div class="col-md-8 d-flex align-items-center position-relative">
          <input type="text" :value="address?.thoroughfare"
                 :id="formId ? `${formId}[thoroughfare]` : 'thoroughfare'"
                 :name="formId ? `${formId}[thoroughfare]` : 'thoroughfare'"
                 class="form-control"
                 ref="thoroughfare_address">
        </div>
        <div class="col-sm-12 has-error text-danger">
          <div v-for="error in fieldErrors?.thoroughfare" class="help-block">{{ error }}</div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <label for="post_town" class="nip-text-color">Town *</label>
        </div>
        <div class="col-md-8 d-flex align-items-center position-relative">
          <input type="text" :value="address?.post_town"
                 :id="formId ? `${formId}[post_town]` : 'post_town'"
                 :name="formId ? `${formId}[post_town]` : 'post_town'"
                 class="form-control"
                 ref="post_town_address">
        </div>
        <div class="col-sm-12 has-error text-danger">
          <div v-for="error in fieldErrors?.post_town" class="help-block">{{ error }}</div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <label for="county" class="nip-text-color">County</label>
        </div>
        <div class="col-md-8 d-flex align-items-center position-relative">
          <input type="text" :value="address?.county"
                 :id="formId ? `${formId}[county]` : 'county'"
                 :name="formId ? `${formId}[county]` : 'county'"
                 class="form-control"
                 ref="county_address">
        </div>
        <div class="col-12 has-error text-danger">
          <div v-for="error in fieldErrors?.county" class="help-block">{{ error }}</div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <label for="postcode" class="nip-text-color">Postcode *</label>
        </div>
        <div class="col-md-8 d-flex align-items-center position-relative">
          <input type="text" :value="address?.postcode"
                 :id="formId ? `${formId}[postcode]` : 'postcode'"
                 :name="formId ? `${formId}[postcode]` : 'postcode'"
                 class="form-control"
                 ref="postcode_address">
        </div>
        <div class="col-12 has-error text-danger">
          <div v-for="error in fieldErrors?.postcode" class="help-block">{{ error }}</div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <label for="country" class="nip-text-color">Country *</label>
        </div>
        <div class="col-md-8 d-flex align-items-center position-relative">
          <select :value="address?.country"
                  :id="formId ? `${formId}[country]` : 'country'"
                  :name="formId ? `${formId}[country]` : 'country'"
                  class="form-select"
                  ref="country_address">
            <option value="">--- Select ---</option>

            <option value="GB-ENG" selected="selected">England</option>

            <option value="GB-WLS">Wales</option>

            <option value="GB-SCT">Scotland</option>

            <option value="GB-NIR">Northern Ireland</option>
          </select>
        </div>
        <div class="col-12 has-error text-danger">
          <div v-for="error in fieldErrors?.country" class="help-block">{{ error }}</div>
        </div>
      </div>
<!--      <div class="pt-4">-->
<!--        <p class="mt-3 mb-2 fw-semibold">How you are listed using the Privacy Service</p>-->
<!--        <p>Your Name <br/>123 Your Home Address <br/>Your town, YR POSTCODE</p>-->
<!--      </div>-->

    </div>
  </template>
  </div>
</template>

<script>
import Tooltip from "@/modules/CompanyIncorporation/components/global/Tooltip.vue";
import Vue, { ref } from "vue";

export default Vue.extend({
  components: {Tooltip},
  props: {
    formId: String,
    fieldValues: Array,
    fieldErrors: Object,
    prefill: Object,
    address: Object,
    shouldRenderAddress: Boolean,
  },
  methods: {
    fillAddress(event){
      this.address = this.prefill.js[event.target.value]
      this.$emit('prefillAddress', this.address)
    }
  },
  setup() {
    const premise_address = ref(null)
    const street_address = ref(null)
    const thoroughfare_address = ref(null)
    const post_town_address = ref(null)
    const county_address = ref(null)
    const postcode_address = ref(null)
    const country_address = ref(null)

    return {
      premise_address,
      street_address,
      thoroughfare_address,
      post_town_address,
      county_address,
      postcode_address,
      country_address,
    }
  }
});
</script>