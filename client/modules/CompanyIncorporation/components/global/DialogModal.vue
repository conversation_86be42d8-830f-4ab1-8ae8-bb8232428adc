<template>
    <div
        class="modal fade"
        :id="modalId"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabindex="-1"
        aria-hidden="true"
        style="z-index: 100000;"
    >
        <div class="modal-dialog modal-dialog-centered custom-modal-width">
            <div class="modal-content p-4" style="border-radius: 0 !important;">
                <div class="modal-header">
                    <h5 class="modal-title fw-semibold nip-text-color">{{title}}</h5>
                    <button type="button"
                            class="btn-close nip-close-modal-icon mr-4"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                            @click="useRejectCallbackToCloseModal && rejectCallback()"></button>
                </div>

                <div class="modal-body">
                    <p>{{mainText}}</p>
                </div>

                <div class="modal-footer">
                    <div class="d-flex gap-2 w-100">
                      <button type="button"
                              class="btn btn-outline-orange btn-view-formation flex-grow-1"
                              data-bs-dismiss="modal"
                              @click="rejectCallback">
                        <span class="fs-6">{{ rejectButtonText }}</span>
                        <i class="fa fa-long-arrow-right mx-3"></i>
                      </button>

                      <button type="button"
                              class="btn btn-orange nip-next-step-button btn-view-formation flex-grow-1"
                              @click="acceptCallback">
                        <span class="fs-6">{{ acceptButtonText }}</span>
                        <i class="fa fa-long-arrow-right mx-3"></i>
                      </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
export default {
    name: 'DialogModal',
    props: {
        modalId: {
            type: String,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        mainText: {
            type: String,
            required: true
        },
        rejectButtonText: {
            type: String,
            default: 'No'
        },
        acceptButtonText: {
            type: String,
            default: 'Yes'
        },
        useRejectCallbackToCloseModal: {
            type: Boolean,
            default: true
        }
    },
    emits: ['rejectCallback', 'acceptCallback'],
    methods: {
        rejectCallback(){
            this.$emit('rejectCallback', {modalId: this.modalId})
        },
        acceptCallback(){
            this.$emit('acceptCallback', {modalId: this.modalId})
        },
    }
}
</script>