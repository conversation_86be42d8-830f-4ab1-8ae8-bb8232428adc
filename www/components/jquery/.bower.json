{"name": "j<PERSON>y", "version": "1.11.3", "main": "dist/jquery.js", "license": "MIT", "ignore": ["**/.*", "build", "dist/cdn", "speed", "test", "*.md", "AUTHORS.txt", "Gruntfile.js", "package.json"], "devDependencies": {"sizzle": "2.1.1-jquery.2.1.2", "requirejs": "2.1.10", "qunit": "1.14.0", "sinon": "1.8.1"}, "keywords": ["j<PERSON>y", "javascript", "library"], "homepage": "https://github.com/jquery/jquery-dist", "_release": "1.11.3", "_resolution": {"type": "version", "tag": "1.11.3", "commit": "1472290917f17af05e98007136096784f9051fab"}, "_source": "https://github.com/jquery/jquery-dist.git", "_target": "~1.11.1", "_originalSource": "j<PERSON>y"}