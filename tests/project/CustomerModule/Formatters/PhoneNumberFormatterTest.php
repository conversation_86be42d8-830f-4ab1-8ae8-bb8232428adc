<?php

namespace project\CustomerModule\Formatters;

use CustomerModule\Formatters\PhoneNumberFormatter;
use PHPUnit\Framework\TestCase;

class PhoneNumberFormatterTest extends TestCase
{
    /**
     * @dataProvider provideInternationalFormatCases
     */
    public function testConvertToInternationalFormat(string $input, string $expected): void
    {
        $output = PhoneNumberFormatter::convertToInternationalFormat($input);
        $this->assertSame($expected, $output, "Failed on convertToInternationalFormat with input: $input");
    }

    /**
     * @dataProvider provideInternationalFormatWithPlusCases
     */
    public function testConvertToInternationalFormatWithPlus(string $input, string $expected): void
    {
        $output = PhoneNumberFormatter::convertToInternationalFormatWithPlus($input);
        $this->assertSame($expected, $output, "Failed on convertToInternationalFormatWithPlus with input: $input");
    }

    /**
     * @dataProvider provideLocalFormatCases
     */
    public function testConvertToLocalFormat(string $input, string $expected): void
    {
        $output = PhoneNumberFormatter::convertToLocalFormat($input);
        $this->assertSame($expected, $output, "Failed on convertToLocalFormat with input: $input");
    }

    public static function provideInternationalFormatCases(): array
    {
        return [
            'empty' => ["", ""],
            'plus440' => ["+4407123456789", "**********89"],
            'double zero 44' => ["004407123456789", "**********89"],
            'plain 440' => ["4407123456789", "**********89"],
            'already clean' => ["**********89", "**********89"],
            'with plus' => ["+**********89", "**********89"],
            'leading 0' => ["07123456789", "**********89"],
            'multiple zeros' => ["0007123456789", "**********89"],
            'non-uk number' => ["123456789", "123456789"],
            'with symbols' => ["+44(07)12345-6789", "**********89"],
            'with dash' => ["+44712345-6789", "**********89"],
            'short variant' => ["+44(07)12345-67", "**********"],
            'garbage input' => ["+(ss))1DFdd23sd", "123"],
        ];
    }

    public static function provideInternationalFormatWithPlusCases(): array
    {
        return [
            'empty' => ["", ""],
            'plus440' => ["+4407123456789", "+**********89"],
            'double zero 44' => ["004407123456789", "+**********89"],
            'plain 440' => ["4407123456789", "+**********89"],
            'already clean' => ["**********89", "+**********89"],
            'with plus' => ["+**********89", "+**********89"],
            'leading 0' => ["07123456789", "+**********89"],
            'non-uk number' => ["123456789", "123456789"],
            'with symbols' => ["+44(07)12345-6789", "+**********89"],
            'with dash' => ["+44712345-6789", "+**********89"],
            'short variant' => ["+44(07)12345-67", "+**********"],
            'garbage input' => ["+(ss))1DFdd23sd", "123"],
        ];
    }

    public static function provideLocalFormatCases(): array
    {
        return [
            'empty' => ["", ""],
            'plus440' => ["+4407123456789", "07123456789"],
            'double zero 44' => ["004407123456789", "07123456789"],
            'plain 440' => ["4407123456789", "07123456789"],
            'already clean' => ["**********89", "07123456789"],
            'with plus' => ["+**********89", "07123456789"],
            'leading 0' => ["07123456789", "07123456789"],
            'missing 0' => ["7123456789", "07123456789"],
            'non-uk number' => ["123456789", "0123456789"],
            'with symbols' => ["+44(07)12345-6789", "07123456789"],
            'with dash' => ["+44712345-6789", "07123456789"],
            'short variant' => ["+44(07)12345-67", "071234567"],
            'garbage input' => ["+(ss))1DFdd23sd", "123"],
        ];
    }
}
