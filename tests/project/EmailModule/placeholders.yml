#first batch
bdg_banking_sweeper:
  markdown: false
  path: Banking/bdg_banking_sweeper.html
  context:
    firstName: '{firstName}'
    companyId: '{companyId}'
  sampleData:
  mocks:

wisebusiness-customers-email: # formerly TransferWise
  markdown: false # must be false because in fact we don't have markdown inside wisebusiness-customers-email.md
  path: Business-Services/WiseBusiness/wisebusiness-customers-email.md
  context:
    firstName: '{firstName}'
  sampleData:
  mocks:

wisebusiness-leads-email: # formerly TransferWise
  markdown: true
  path: Business-Services/WiseBusiness/wisebusiness-leads-email.md
  context: []
  sampleData:
  mocks:

incorp_accepted:
  markdown: false
  path: Incorporation/incorp_accepted.html
  context:
    firstName: '{firstName}'
    companyInfoUrl: '{companyInfoUrl}'
    companyName: '{companyName}'
    companyNumber: '{companyNumber}'
  sampleData:
  mocks:

incorporation_documents_resent:
  markdown: false
  path: Incorporation/incorporation_documents_resent.html
  context:
    firstName: '{firstName}'
  sampleData:
  mocks:

incorporation_rejected:
  markdown: false
  path: Incorporation/incorporation_rejected.html
  context:
    firstName: '{firstName}'
    rejectText: Documentation error, Id not recognize
    companyName: '{companyName}'
  sampleData:
  mocks:

incorporation_submitted:
  markdown: false
  path: Incorporation/incorporation_submitted.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  sampleData:
  mocks:

llp_incorporation_accepted:
  markdown: false
  path: Incorporation/llp_incorporation_accepted.html
  context:
    firstName: '{firstName}'
    companyInfoUrl: test
    companyName: '{companyName}'
    companyNumber: '{companyNumber}'
  sampleData:
  mocks:

reserved_incorp_accept:
  markdown: false
  path: Incorporation/reserved_incorp_accept.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    companyNumber: '{companyNumber}'
    date: '{date}'
  sampleData:
  mocks:

reserved_incorp_rejected:
  markdown: false
  path: Incorporation/reserved_incorp_rejected.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  sampleData:
  mocks:

share_certificates_emailed:
  markdown: false
  path: Misc/share_certificates_emailed.html
  context:
    firstName: '{firstName}'
  sampleData:
  mocks:

claim-free-day-pass-email:
  markdown: false
  path: Offers/claim-free-day-pass-email.html
  context: []
  mocks:

free-day-pass-email:
  markdown: false
  path: Offers/free-day-pass-email.html
  context:
    firstName: '{firstName}'
    claimUrl: '{claimUrl}'
    otp: '{otp}'
  mocks:

#second batch

brookson:
  markdown: true
  path: Offers/Toolkit/Brookson/brookson.md
  context:
    firstName: '{firstName}'
  mocks:

brooksonLead:
  markdown: true
  path: Offers/Toolkit/Brookson/brooksonLead.md
  context:
    fullName: '{fullName}'
    email: '{email}'
    phone: '{phone}'
    companyNumber: '{companyNumber}'
    companyName: '{companyName}'
    companySicCodes:
      -
        Code: 1245
        Description: '{Description}'
      -
        Code: 1235
        Description: '{Description}'
  mocks:

banking_sweeper:
  markdown: false
  path: Banking/Banking-Sweeper/banking_sweeper.html
  context:
    firstName: '{firstName}'
    cashBackAmount: '{cashBackAmount}'
    oneTimePassword: '{oneTimePassword}'
    companyId: '{companyId}'
  mocks:

freeAgent:
  markdown: false
  path: Business-Services/FreeAgent/freeAgent.html
  context:
    firstName: '{firstName}'
  mocks:

forgotten_password:
  markdown: false
  path: Customer/forgotten_password.html
  context:
    firstName: '{firstName}'
    tokenLinkAddress: https://cms/change-password/?tokenString=********-50f3-4e18-b2fe-**********
  mocks:

tsb-to-customer:
  markdown: false
  path: Banking/TSB/tsb-to-customer.html
  context:
    firstName: '{firstName}'
  mocks:

tsb-reminder-customer:
  markdown: false
  path: Banking/TSB/tsb-reminder-customer.html
  context:
    firstName: '{firstName}'
  mocks:

service-completed:
  markdown: false
  path: Annual-Return-Service/service-completed.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  mocks:
    companyIdInfo:
      class: \EmailModule\Mock\CompanyMock

ch-accepted:
  markdown: false
  path: Annual-Return-Service/ch-accepted.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  mocks:

customer-accepted:
  markdown: false
  path: Annual-Return-Service/customer-accepted.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    companyNumber: '{companyNumber}'
  mocks:

tide_for_customers:
  markdown: false
  path: Banking/Tide/tide_for_customers.html
  context:
    subject: '{subject}'
    firstName: '{firstName}'
    email: '{email}'
  mocks:

tide_for_international_customers:
  markdown: false
  path: Banking/Tide/tide_for_international_customers.html
  context:
    firstName: '{firstName}'
    subject: '{subject}'
  sampleData:
  mocks:

#third batch

abandoned_basket:
  markdown: false
  path: Reminders/abandoned_basket.html
  context:
    firstName: '{firstName}'
    products: ['Register office', 'mail fowarding']
    url: '{url}'
  sampleData:
  mocks:

abandoned_basket_formations:
  markdown: false
  path: Reminders/abandoned_basket_formations.html
  context:
    firstName: '{firstName}'
    formationPackage: privacy_packages
    url: '{url}'
  sampleData:
  mocks:

namesco_voucher:
  markdown: true
  path: Offers/Toolkit/Namesco/namesco_voucher.md
  context:
    voucher: '{voucher}'
    domainName: '{domainName}'
  placeHolder:
  mocks:

edu-02-months-psc:
  markdown: false
  path: Education-emails/edu-02-months-psc.html
  context:
    firstName: '{firstName}'
  sampleData:
    services:
      -
        productId: 1729
        dtExpires: '01/01/2025'
      -
        productId: 1729
        dtExpires: '01/01/2025'

edu-02-months-psc-cta:
  markdown: false
  path: Education-emails/edu-02-months-psc-cta.html
  context:
    firstName: '{firstName}'
    otp: '{otp}'

edu-04-months-registered-office:
  markdown: false
  path: Education-emails/edu-04-months-registered-office.html
  context:
    firstName: '{firstName}'
  sampleData:
    services:
      -
        productId: 153
        dtExpires: '01/01/2025'
      -
        productId: 153
        dtExpires: '01/01/2025'

edu-04-months-registered-office-cta:
  markdown: false
  path: Education-emails/edu-04-months-registered-office-cta.html
  context:
    firstName: '{firstName}'
    otp: '{otp}'

edu-06-months-fraud:
  markdown: false
  path: Education-emails/edu-06-months-fraud.html
  context:
    firstName: '{firstName}'
  sampleData:
    services:
      -
        productId: 201
        dtExpires: '01/01/2025'
      -
        productId: 201
        dtExpires: '01/01/2025'

edu-06-months-fraud-cta:
  markdown: false
  path: Education-emails/edu-06-months-fraud-cta.html
  context:
    firstName: '{firstName}'
    otp: '{otp}'

edu-08-months-business-address:
  markdown: false
  path: Education-emails/edu-08-months-business-address.html
  context:
    firstName: '{firstName}'
    otp: '{otp}'

edu-10-months-director-responsibilities:
  markdown: false
  path: Education-emails/edu-10-months-director-responsibilities.html
  context:
    firstName: '{firstName}'
    otp: '{otp}'
  sampleData:
    services:
      -
        productId: 201
        dtExpires: '01/01/2025'
      -
        productId: 201
        dtExpires: '01/01/2025'

iwoca:
  markdown: false
  path: Offers/iwoca.html
  context:
    firstName: '{firstName}'

taxassist:
  markdown: false
  path: Offers/Toolkit/TaxAssist/taxassist.html
  context:
    firstName: '{firstName}'

ebook:
  markdown: false
  path: Offers/Toolkit/Ebook/ebook.html
  context:
    firstName: '{firstName}'

customers-facebook-community:
  markdown: false
  path: Offers/Toolkit/Customers-Facebook-Community/customers-facebook-community.html
  context:
    firstName: '{firstName}'

soon_expires:
  markdown: false
  path: Customer/soon_expires.html
  context:
    firstName: '{firstName}'
    last4Digits: '{last4Digits}'

expired:
  markdown: false
  path: Customer/expired.html
  context:
    firstName: '{firstName}'
    last4Digits: '{last4Digits}'
    manageCardUrl: '{manageCardUrl}'

submission-queue-reminder:
  markdown: false
  path: Customer/Id-Check/submission-queue-reminder.latte
  context:
    customerName: '{customerName}'
    showValidateAddress: true
  sampleData: []
  mocks:
    companyInfo:
      class: \EmailModule\Mock\CompanyMock
    entity:
      class: \EmailModule\Mock\EntityInfoMock

mailscan-no-service:
  markdown: true
  path: Digital-Mailroom/mailscan-no-service.md
  context:
    postItem: #this will be created
    customer: #this will be created
  sampleData: []
  mocks:
    customer:
      class: \EmailModule\Mock\CustomerMock
    postItem:
      class: \EmailModule\Mock\CompanyMock

mailscan-service-suspended:
  markdown: true
  path: Digital-Mailroom/mailscan-service-suspended.md
  context:
    companyId: 255
  sampleData: []
  mocks:
    serviceView:
      class: \EmailModule\Mock\ServiceMock
    postItem:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

mailscan-service-active-id-check:
  markdown: true
  path: Digital-Mailroom/mailscan-service-active-id-check.md
  context: []
  sampleData:
    postItem:
      companyName: '{companyName}'
  mocks:
    postItem:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

mailscan-service-active-no-id-check:
  markdown: true
  path: Digital-Mailroom/mailscan-service-active-no-id-check.md
  context: []
  sampleData: []
  mocks:
    company:
      class: \EmailModule\Mock\CompanyMock
    postItem:
      class: \EmailModule\Mock\PostItemMock
    customer:
      class: \EmailModule\Mock\CustomerMock

mailscan-service-overdue-id-check:
  markdown: true
  path: Digital-Mailroom/mailscan-service-overdue-id-check.md
  context:
    postItem:
    companyId: 300
  sampleData:
    customer: true
    postItem:
      companyName: '{companyName}'
    serviceView:
      productId: 153
      dtExpires: '01/01/2025'
  mocks:
    postItem:
      class: \EmailModule\Mock\PostItemMock
    serviceView:
      class: \EmailModule\Mock\ServiceMock
    customer:
      class: \EmailModule\Mock\CustomerMock

mailscan-service-overdue-no-id-check:
  markdown: true
  path: Digital-Mailroom/mailscan-service-overdue-no-id-check.md
  context:
    companyId: 300
  sampleData: []
  mocks:
    postItem:
      class: \EmailModule\Mock\CompanyMock
    serviceView:
      class: \EmailModule\Mock\ServiceMock
    customer:
      class: \EmailModule\Mock\CustomerMock

id-validated:
  markdown: true
  path: Customer/Id-Check/id-validated.md
  context: []
  sampleData: []
  mocks:
    companyInfo:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

id-invalid:
  markdown: true
  path: Customer/Id-Check/id-invalid.md
  context: []
  sampleData: []
  mocks:
    companyInfo:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

id-link:
  markdown: false
  path: Customer/Id-Check/id-link.latte
  context:
    entityId: '{entityId}'
  sampleData: []
  mocks:
    company:
      class: \EmailModule\Mock\CompanyMock

id-nullify:
  markdown: true
  path: Customer/Id-Check/id-nullify.md
  context: []
  sampleData: []
  mocks:
    companyInfo:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

id-help:
  markdown: true
  path: Customer/Id-Check/id-help.md
  context: []
  sampleData: []
  mocks:
    company:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

id-summary:
  markdown: true
  path: Customer/Id-Check/id-summary.md
  context: []
  sampleData: []
  mocks:
    customer:
      class: \EmailModule\Mock\CustomerMock
    customerInfo:
      class: \EmailModule\Mock\CustomerMock

id-company-summary:
  markdown: true
  path: Customer/Id-Check/id-company-summary.md
  context: []
  sampleData: []
  mocks:
    customer:
      class: \EmailModule\Mock\CustomerMock
    companyInfo:
      class: \EmailModule\Mock\CompanyMock

id-termination:
  markdown: true
  path: Customer/Id-Check/id-termination.md
  context: []
  sampleData: []
  mocks:
    customer:
      class: \EmailModule\Mock\CustomerMock
    company:
      class: \EmailModule\Mock\CompanyMock

id-termination-warning:
  markdown: true
  path: Customer/Id-Check/id-termination-warning.md
  context: []
  sampleData:
    endDate: '2025-01-01' # must be a valid date
  mocks:
    customer:
      class: \EmailModule\Mock\CustomerMock
    customerInfo:
      class: \EmailModule\Mock\CustomerMock
    companyInfo:
      class: \EmailModule\Mock\CompanyMock

Renewal-Reminder-D-21-Before:
  markdown: false
  path: Service-Renewals/before_expires.latte
  context:
    firstName: '{firstName}'
    renewalDate: '{renewalDate}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: Renewal-Reminder-D-21-Before
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Reminder-D-10-Before:
  markdown: false
  path: Service-Renewals/before_expires.latte
  context:
    firstName: '{firstName}'
    renewalDate: '{renewalDate}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: Renewal-Reminder-D-10-Before
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Reminder-D-7-Before:
  markdown: false
  path: Service-Renewals/before_expires.latte
  context:
    firstName: '{firstName}'
    renewalDate: '{renewalDate}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: Renewal-Reminder-D-7-Before
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Reminder-D+1-after:
  markdown: false
  path: Service-Renewals/expired_1.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: "Renewal-Reminder-D1-after" #this is modify in the link
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Reminder-D+7-after:
  markdown: false
  path: Service-Renewals/expired_7.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: "Renewal-Reminder-D7-after" #this is modify in the link
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Reminder-D+14-after:
  markdown: false
  path: Service-Renewals/expired_14.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: "Renewal-Reminder-D14-after" #this is modify in the link
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Reminder-Credit-Control-D+21-after:
  markdown: false
  path: Service-Renewals/expired_21.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: "Renewal-Reminder-Credit-Control-D21-after" #this is modify in the link
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Reminder-Credit-Control-D+28-after:
  markdown: false
  path: Service-Renewals/expired_28.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: "Renewal-Reminder-Credit-Control-D28-after" #this is modify in the link
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Reminder-Credit-Control-D+30-after:
  markdown: false
  path: Service-Renewals/expired_30.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: "Renewal-Reminder-Credit-Control-D30-after" #this is modify in the link
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    subject: '{subject}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:

Renewal-Auto-D-21-before:
  markdown: false
  path: Service-Renewals/auto_renewal.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    subject: '{subject}'
    renewalDate: '{renewalDate}'
    emailName: Renewal-Auto-D-21-before
    cardInfo: '{cardInfo}'
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    cardInfo: true
    benefits: true
  mocks:

Renewal-Auto-D-7-before:
  markdown: false
  path: Service-Renewals/auto_renewal.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    subject: '{subject}'
    renewalDate: '{renewalDate}'
    emailName: Renewal-Auto-D-7-before
    cardInfo: 0006
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    cardInfo: true
    benefits: true
  mocks:

Renewal-Auto-Succeeded:
  markdown: false
  path: Service-Renewals/success.latte
  context:
    subject: '{subject}'
    firstName: '{firstName}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: 1778
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    cardInfo: true
    benefits: true
  mocks:

Renewal-Auto-Fail:
  markdown: false
  path: Service-Renewals/fail.latte
  context:
    firstName: '{firstName}'
    subject: '{subject}'
    companyName: '{companyName}'
    oneTimePassword: '{oneTimePassword}'
    emailName: 1778
    currentYear: '{currentYear}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    otpWalletLink: '{otpWalletLink}'
    isWholesale: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    cardInfo: true
    benefits: true
  mocks:

#Sixth batch

PSC-Sweeper:
  markdown: false
  path: Reminders/PSC-Sweeper/sweeper.email.latte
  context:
    firstName: '{firstName}'
    subject: '{subject}'
    otp: '{otp}'
  sampleData:
  mocks:

UpsellFailed:
  markdown: false
  path: Incorporation/Upsell/upsellFailed.latte
  context:
    oneTimePassword: '{oneTimePassword}'
    emailTitle: '{emailTitle}'
  sampleData:
    customer: true
    company: true
    services:
    -
      productId: 1729
      dtExpires: '01/01/2025'
  mocks:

tsb-leads-daily:
  markdown: true
  path: Banking/TSB/tsb-leads-daily.md
  context:
    empty: true
  sampleData:
  mocks:

tsb-leads-weekly:
  markdown: true
  path: Banking/TSB/tsb-leads-weekly.md
  context:
    empty: true
  sampleData:
  mocks:

tsb-leads-monthly:
  markdown: true
  path: Banking/TSB/tsb-leads-monthly.md
  context:
    empty: true
  sampleData:
  mocks:

##seventh batch

ch-rejected:
  markdown: false
  path: Annual-Return-Service/ch-rejected.html
  context:
    companyName: '{companyName}'
    companyNumber: '{companyNumber}'
  sampleData:
  mocks:

ch-error:
  markdown: false
  path: Annual-Return-Service/ch-error.html
  context:
    companyName: '{companyName}'
    companyId: '{companyId}'
  sampleData:
  mocks:

ch-empty-incorporation-certificate-error:
  markdown: false
  path: Annual-Return-Service/ch-empty-incorporation-certificate-error.html
  context:
    companyId: '{companyId}'
    formSubmissionId: '{formSubmissionId}'
  sampleData:
  mocks:

sync-all-complete-customer:
  markdown: false
  path: Customer/sync-all-complete-customer.html
  context:
    firstName: '{firstName}'
  sampleData:
  mocks:

sync-all-complete-admin:
  markdown: false
  path: Misc/sync-all-complete-admin.html
  context:
    staffFirstName: '{staffFirstName}'
    customerEmail: '{customerEmail}'
    customerId: '{customerId}'
  sampleData:
  mocks:

id-failure-with-retry:
  markdown: false
  path: Customer/Id-Check/id-failure-with-retry.md
  context:
    entity: '{entity}'
    allowToRetry: true
  sampleData:
    company: true
  mocks:
    company:
      class: \EmailModule\Mock\CompanyMock

id-failure-with-retry-retail:
  markdown: false
  path: Customer/Id-Check/id-failure-with-retry-retail.md
  context:
    entity: '{entity}'
    allowToRetry: true
  sampleData: []
  mocks:
    company:
      class: \EmailModule\Mock\CompanyMock

id-failure-with-retry-wholesale:
  markdown: false
  path: Customer/Id-Check/id-failure-with-retry-wholesale.md
  context:
    entity: '{entity}'
    allowToRetry: true
  sampleData:
    company: true
  mocks:
    company:
      class: \EmailModule\Mock\CompanyMock

internal-order-confirmation:
  markdown: false
  path: Misc/internal-order-confirmation.html
  context:
    orderId: '{orderId}'
    customerName: '{customerName}'
    customerEmail: '{customerEmail}'
    customerPhone: '{customerPhone}'
    address: '{address}'
    additional: '{additional}'
    productName: '{productName}'
    amount: '{amount}'
  sampleData:
  mocks:

order-confirmation:
  markdown: false
  path: Customer/Orders/order-confirmation.html
  context:
    firstName: '{firstName}'
    lastName: '{lastName}'
    productsEmailText: Thank you for purchasing the Comprehensive Package.
  sampleData:
  mocks:

form-10-get-20:
  markdown: false
  path: Customer/Orders/form-10-get-20.html
  context:
    firstName: '{firstName}'
  sampleData:
  mocks:

new-customer-payment-page:
  markdown: false
  path: Customer/new-customer-payment-page.html
  context:
    fullName: '{fullName}'
    password: '{password}'
    username: '{username}'
  sampleData:
  mocks:

rejected-confirmation-statement:
  markdown: false
  path: Annual-Return-Service/rejected-confirmation-statement.html
  context:
    firstName: '{firstName}'
    rejectText: '{rejectText}'
    companyName: '{companyName}'
    companyId: '{companyId}'
  sampleData:
  mocks:

annual-return-accepted:
  markdown: false
  path: Annual-Return-Service/annual-return-accepted.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  sampleData:
  mocks:

dy-ar-error:
  markdown: false
  path: Annual-Return-Service/dy-ar-error.md
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  sampleData:
  mocks:

missing-info:
  markdown: false
  path: Annual-Return-Service/missing-info.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  sampleData:
  mocks:

customer-rejected:
  markdown: false
  path: Annual-Return-Service/customer-rejected.html
  context:
    companyName: '{companyName}'
    message: '{message}'
    companyId: '{companyId}'
  sampleData:
  mocks:

error-email:
  markdown: false
  path: Misc/error-email
  context:
    date: '{date}'
    message: '{message}'
  sampleData:
  mocks:

name-change-accepted:
  markdown: false
  path: Incorporation/Company-Change/name-change-accepted.html
  context:
    companyName: '{companyName}'
    firstName: '{firstName}'
  sampleData:
  mocks:

name-change-rejected:
  markdown: false
  path: Incorporation/Company-Change/name-change-rejected.html
  context:
    companyName: '{companyName}'
    firstName: '{firstName}'
    rejectText: '{rejectText}'
  sampleData:
  mocks:

barclays-error:
  markdown: false
  path: Banking/Barclays/barclays-error.html
  context:
    companyId: '{companyId}'
    error: '{error}'
  sampleData:
  mocks:

customer-refund:
  markdown: false
  path: Customer/customer-refund.html
  context:
    firstName: '{firstName}'
    orderId: '{orderId}'
    refundAmount: '{refundAmount}'
  sampleData:
  mocks:

ch-internal-failure:
  markdown: false
  path: Annual-Return-Service/ch-internal-failure.html
  context:
    companyId: '{companyId}'
    formSubmissionId: '{formSubmissionId}'
  sampleData:
  mocks:

cashbacks-paid:
  markdown: false
  path: Banking/cashbacks-paid.html
  context:
    firstName: '{firstName}'
    companiesTable: '{companiesTable}'
    cashbackInfo: '{cashbackInfo}'
  sampleData:
  mocks:

wholesale-general-registration:
  markdown: false
  path: Customer/wholesale-general-registration.html
  context:
    firstName: '{firstName}'
    email: '{email}'
    password: '{password}'
  mocks:

confirmation_statement_auth_code:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_auth_code.html
  context:
    empty: true
  mocks:

confirmation_statement_dyi_date_reminder_d_minus_7:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_dyi_date_reminder_d_minus_7.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    daysToDueDate: '{daysToDueDate}'
    dueDate: '{dueDate}'

confirmation_statement_dyi_date_reminder_d_minus_14:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_dyi_date_reminder_d_minus_14.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    daysToDueDate: '{daysToDueDate}'
    dueDate: '{dueDate}'
  mocks:

confirmation_statement_dyi_date_reminder_d_plus_1:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_dyi_date_reminder_d_plus_1.html
  context:
    firstName: '{firstName}'
    dueDate: '{dueDate}'
    dateExpire: '{dateExpire}'
  mocks:

confirmation_statement_pending_issue_termination:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_pending_issue_termination.html
  context:
    dueDate: '{dueDate}'
  mocks:

confirmation_statement_service_date_reminder_d_minus_14:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_service_date_reminder_d_minus_14.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    dueDate: '{dueDate}'
    daysToDueDate: '{daysToDueDate}'
  sampleData:
  mocks:

confirmation_statement_service_date_reminder_d_minus_28:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_service_date_reminder_d_minus_28.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    dueDate: '{dueDate}'
  mocks:

confirmation_statement_service_date_reminder_d_plus_1:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_service_date_reminder_d_plus_1.html
  context:
    firstName: '{firstName}'
    dueDate: '{dueDate}'
  mocks:

confirmation_statement_service_pending_approval_reminder:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_service_pending_approval_reminder.html
  context:
    isOrWasText: is
    datePrepared: '{datePrepared}'
    dueDate: '{dueDate}'
  mocks:

confirmation_statement_service_pending_approval_termination:
  markdown: false
  path: Annual-Return-Service/confirmation_statement_service_pending_approval_termination.html
  context:
    datePrepared: '{datePrepared}'
    dueDate: '{dueDate}'
  mocks:

service_completed:
  markdown: false
  path: Annual-Return-Service/service-completed.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  mocks:

first-email-to-customer:
  markdown: false
  path: Business-Services/Crunch/first-email-to-customer.html
  context:
    subject: '{subject}'
    firstName: '{firstName}'
    currentYear: '{currentYear}'
  mocks:

second-email-to-customer:
  markdown: false
  path: Business-Services/Crunch/second-email-to-customer.html
  context:
    subject: '{subject}'
    firstName: '{firstName}'
    currentYear: '{currentYear}'
  sampleData:
  mocks:

payoneer-customers-email:
  markdown: false
  path: Business-Services/Payoneer/payoneer-customers-email.md
  context: []
  sampleData:
  mocks:

payoneer-leads-email:
  markdown: false
  path: Business-Services/Payoneer/payoneer-leads-email.md
  context: []
  sampleData:
  mocks:

company-seal-and-stamp:
  markdown: false
  path: Products/Company-Seal-And-Stamps/company-seal-and-stamp.latte
  context:
    productName: '{productName}'
  sampleData:
    company: true
    listOfCompaniesSeals: true
  mocks:

company_stamp_email:
  markdown: false
  path: Products/Company-Seal-And-Stamps/company-seal-and-stamp.latte
  context:
    productName: '{productName}'
  sampleData:
    company: true
    listOfCompaniesStamp: true
  mocks:

xeinadin-customer-email:
  markdown: false
  path: Offers/Toolkit/Xeinadin/xeinadin-customer-email.html
  context:
    firstName: '{firstName}'
  sampleData:
  mocks:

xeinadin-leads-email:
  markdown: false
  path: Offers/Toolkit/Xeinadin/xeinadin-leads-email.html
  context: []
  sampleData:
  mocks:

yell-customer-email:
  markdown: false
  path: Offers/Toolkit/Yell/yell-customer-email.html
  context:
    firstName: '{firstName}'
  sampleData:
  mocks:

claim_workhub_passes_initial_ultimate_package:
  markdown: false
  path: Products/claim_workhub_passes_initial_ultimate_package.html
  context:
    firstName: '{firstName}'
  sampleData: []
  mocks:

claim_workhub_passes_renewal_ultimate_package:
  markdown: false
  path: Products/claim_workhub_passes_renewal_ultimate_package.html
  context:
    firstName: '{firstName}'
  sampleData: []
  mocks:

id-link-debranded:
  markdown: false
  path: Customer/Id-Check/id-link-debranded.latte
  context:
    entityId: '{entityId}'
  sampleData: []
  mocks:
    company:
      class: \EmailModule\Mock\CompanyMock

failed-direct-debit-payment-email:
  markdown: false
  path: Direct-Debit/failed-direct-debit-payment-email.latte
  context:
    companyName: '{companyName}'
  sampleData:
    customer: true
    services:
      -
        productId: 201
        dtExpires: '2021-01-23'
      -
        productId: 201
        dtExpires: '2021-01-23'

checkRow:
  markdown: false
  path: Customer/Id-Check/Blocks/checkRow.latte
  context:  []
  sampleData: []
  mocks:
    companyInfo:
      class: \EmailModule\Mock\CompanyMock
    checkInfo:
      class: \EmailModule\Mock\CheckInfoMock
    entityInfo:
      class: \EmailModule\Mock\EntityInfoMock

# block file
idFailedForCompanyWithNotifications:
  markdown: false
  path: Customer/Id-Check/Blocks/idFailedForCompanyWithNotifications.latte
  context:  []
  sampleData: []
  mocks:
    companyInfo:
      class: \EmailModule\Mock\CompanyMock
    entityInfo:
      class: \EmailModule\Mock\EntityInfoMock

# block file
idNullifyForCompany:
  markdown: false
  path: Customer/Id-Check/Blocks/idNullifyForCompany.latte
  context:  []
  sampleData: []
  mocks:
    companyInfo:
      class: \EmailModule\Mock\CompanyMock
    entityInfo:
      class: \EmailModule\Mock\EntityInfoMock

# block file
idStatusForCompany:
  markdown: false
  path: Customer/Id-Check/Blocks/idStatusForCompany.latte
  context:  []
  sampleData: []
  mocks:
    companyInfo:
      class: \EmailModule\Mock\CompanyMock
    checkInfo:
      class: \EmailModule\Mock\CheckInfoMock
    entityInfo:
      class: \EmailModule\Mock\EntityInfoMock

# block file
idStatusForCustomer:
  markdown: false
  path: Customer/Id-Check/Blocks/idStatusForCustomer.latte
  context:  []
  sampleData:
    customer: true
    company: true
    companyInfo: true
    services:
      -
        productId: 201
        dtExpires: '2021-01-23'
      -
        productId: 201
        dtExpires: '2021-01-23'
  mocks:
    customerInfo:
      class: \EmailModule\Mock\CustomerMock

idStatusForCustomerInvalid:
  markdown: false
  path: Customer/Id-Check/Blocks/idStatusForCustomerInvalid.latte
  context: []
  sampleData: []
  mocks:
    customerInfo:
      class: \EmailModule\Mock\CustomerMock
    companyInfo:
      class: \EmailModule\Mock\CompanyMock

serviceRenewalForm:
  markdown: false
  path: Digital-Mailroom/serviceRenewalForm.latte
  context:
    companyId: '{companyId}'
  sampleData: []
  mocks:
    serviceView:
      class: \EmailModule\Mock\ServiceMock

payment-retried-by-card-email:
  markdown: false
  path: Direct-Debit/payment-retried-by-card-email.latte
  context:
    companyName: '{companyName}'
    walletLink: '{walletLink}'
  sampleData:
    customer: true
    services:
      -
        productId: 201
        dtExpires: '2021-01-23'
      -
        productId: 201
        dtExpires: '2021-01-23'

payment-retry-email:
  markdown: false
  path: Direct-Debit/payment-retry-email.latte
  context:
    companyName: '{companyName}'
    walletLink: '{walletLink}'
  sampleData:
    customer: true
    services:
      -
        productId: 201
        dtExpires: '2021-01-23'
      -
        productId: 201
        dtExpires: '2021-01-23'

payment-retry-last-email:
  markdown: false
  path: Direct-Debit/payment-retry-last-email.latte
  context:
    companyName: '{companyName}'
    walletLink: '{walletLink}'
  sampleData:
    customer: true
    services:
      -
        productId: 201
        dtExpires: '2021-01-23'
      -
        productId: 201
        dtExpires: '2021-01-23'

incorporation_pending_id_check:
  markdown: false
  path: Incorporation/incorporation_pending_id_check.latte
  context:
    companyName: '{companyName}'
    firstName: '{firstName}'
    idStatuses:
      -
        name: "{name}"
        roles: "{roles}"
        isVerified: true
      -
        name: "{name}"
        roles: "{roles}"
        isVerified: false
        otp: "{otp}"
      -
        name: "{name}"
        roles: "{roles}"
        isVerified: true

upsellFailed:
  markdown: false
  path: Incorporation/Upsell/upsellFailed.latte
  context:
      emailTitle: '{emailTitle}'
  sampleData:
    customer: true
    company: true
    services:
      -
        productId: 201
        dtExpires: '2021-01-23'
      -
        productId: 201
        dtExpires: '2021-01-23'
  mocks:
    customer:
      class: \EmailModule\Mock\CustomerMock
    company:
      class: \EmailModule\Mock\CompanyMock

sweeper.email:
  markdown: false
  path: Reminders/PSC-Sweeper/sweeper.email.latte
  context:
    firstName: '{firstName}'

auto_renewal:
  markdown: false
  path: Service-Renewals/auto_renewal.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    renewalDate: '{renewalDate}'
    isOmnipay: true
    cardType: '{cardType}'
    cardNumber: '{cardNumber}'
    isRenewalAfterNewPrivacy: false
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
  sampleData:
    services: true
    benefits: true
  mocks:
    cardInfo:
      class: \EmailModule\Mock\CardMock

before_expires:
  markdown: false
  path: Service-Renewals/before_expires.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    renewalDate: '{renewalDate}'
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
    serviceAddressType: true
  sampleData:
    services: true
    benefits: true

before_expires_comprehensive:
  markdown: false
  path: Service-Renewals/before_expires_comprehensive.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    ultimatePackageRenewalPrice: '{ultimatePackageRenewalPrice}'
    registeredOfficePackagePrice: '{registeredOfficePackagePrice}'
    confirmationStatementPackagePrice: '{confirmationStatementPackagePrice}'
    privacyPackagePrice: '{privacyPackagePrice}'
  sampleData:
    services: true
    benefits: true
  mocks:
    service:
      class: \EmailModule\Mock\ServiceMock

comprehensiveUpgrade:
  markdown: false
  path: Service-Renewals/comprehensiveUpgrade.latte
  context:
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'

direct-debit:
  markdown: false
  path: Service-Renewals/direct-debit.latte
  context:
    isWholesale: false
    otpWalletLink: '{otpWalletLink}'
  sampleData:
  mocks:

expired_1:
  markdown: false
  path: Service-Renewals/expired_1.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
  sampleData:
    services: true
    benefits: true
  mocks:

expired_7:
  markdown: false
  path: Service-Renewals/expired_7.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
  sampleData:
    services: true
    benefits: true
  mocks:

expired_14:
  markdown: false
  path: Service-Renewals/expired_14.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
  sampleData:
    services: true
    benefits: true
  mocks:

expired_21:
  markdown: false
  path: Service-Renewals/expired_21.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
  sampleData:
    benefits: true
  mocks:

expired_28:
  markdown: false
  path: Service-Renewals/expired_28.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
  sampleData:
    benefits: true
  mocks:

expired_30:
  markdown: false
  path: Service-Renewals/expired_30.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
    serviceAddressType: true
    latePaymentTriggeringDate: '{latePaymentTriggeringDate}'
  sampleData:
    benefits: true
  mocks:

fail:
  markdown: false
  path: Service-Renewals/fail.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    isNewUltimatePackage: true
    isRenewalAfterNewPrivacy: true
    comprehensivePackageRenewalPrice: '{comprehensivePackageRenewalPrice}'
    serviceAddressType: true
  sampleData:
    services: true
    benefits: true
  mocks:
    services:
      class: \EmailModule\Mock\ServiceMock

success:
  markdown: false
  path: Service-Renewals/success.latte
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    currentYear: '{currentYear}'
    isOmnipay: true
    cardType: '{cardType}'
    cardNumber: '{cardNumber}'
  sampleData:
    services: true
  mocks:
    cardInfo:
      class: \EmailModule\Mock\CardMock

footer-new-ultimate-after:
  markdown: false
  path: Service-Renewals/footer-new-ultimate-after.latte
  context: []
  sampleData: []
  mocks:

footer-new-ultimate-fail:
  markdown: false
  path: Service-Renewals/footer-new-ultimate-fail.latte
  context:
    currentYear: '{currentYear}'
  sampleData: []
  mocks:

footer-new-ultimate:
  markdown: false
  path: Service-Renewals/footer-new-ultimate.latte
  context:
    currentYear: '{currentYear}'
  sampleData: []
  mocks:

footer:
  markdown: false
  path: Service-Renewals/footer.latte
  context:
    currentYear: '{currentYear}'
  sampleData: []
  mocks:

list-of-companies:
  markdown: false
  path: Products/Company-Seal-And-Stamps/block/list-of-companies.latte
  context: []
  sampleData:
    listOfCompaniesStamp: true
  mocks:

full-privacy-regular-price-manual-renewal:
  markdown: false
  path: Service-Renewals/full-privacy-regular-price-manual-renewal.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    monthlyPrice: '{monthlyPrice}'
  sampleData: []
  mocks:

barclaycard-acquiring-customers-email:
  markdown: false
  path: Business-Services/Barclaycard/barclaycard-acquiring-customers-email.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    customerEmail: '{customerEmail}'
  sampleData:
  mocks:

barclaycard-issuing-and-acquiring-customers-email:
  markdown: false
  path: Business-Services/Barclaycard/barclaycard-issuing-and-acquiring-customers-email.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    customerEmail: '{customerEmail}'
  sampleData:
  mocks:

barclaycard-issuing-customers-email:
  markdown: false
  path: Business-Services/Barclaycard/barclaycard-issuing-customers-email.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    customerEmail: '{customerEmail}'
  sampleData:
  mocks:

hsbc-optin-customers-email:
  markdown: false
  path: Business-Services/HSBC/hsbc-optin-customers-email.html
  context: []
  sampleData:
  mocks:

company-monitoring-email:
  markdown: false
  path: Company-Monitoring/company-monitoring-email.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  sampleData:
  mocks:
    changes:
      class: \EmailModule\Mock\ChangesMock

fraud-protection-email:
  markdown: false
  path: Company-Monitoring/fraud-protection-email.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
  sampleData:
  mocks:
    changes:
      class: \EmailModule\Mock\ChangesMock

mail-no-service-cta:
  markdown: false
  path: Digital-Mailroom/Mailbox-Emails/mail-no-service-cta.html
  context: []
  sampleData:
  mocks:

mail-released-scan-above-quota-cta:
  markdown: false
  path: Digital-Mailroom/Mailbox-Emails/mail-released-scan-above-quota-cta.html
  context: []
  sampleData:
  mocks:

mail-waiting-payment-cta:
  markdown: false
  path: Digital-Mailroom/Mailbox-Emails/mail-waiting-payment-cta.html
  context: []
  sampleData:
  mocks:

parcel-no-service-cta:
  markdown: false
  path: Digital-Mailroom/Mailbox-Emails/parcel-no-service-cta.html
  context: []
  sampleData:
  mocks:

parcel-rts-cta:
  markdown: false
  path: Digital-Mailroom/Mailbox-Emails/parcel-rts-cta.html
  context: []
  sampleData: []
  mocks:

yell-leads-email:
  markdown: false
  path: Offers/Toolkit/Yell/yell-leads-email.html
  context: []
  sampleData: []
  mocks:

purchase-link-email:
  markdown: false
  path: PurchaseLinkEmail/purchase-link-email.html
  context:
    customerName: '{customerName}'
  sampleData: []
  mocks:

full-privacy-regular-price-auto-renewal:
  markdown: false
  path: Service-Renewals/full-privacy-regular-price-auto-renewal.html
  context:
    firstName: '{firstName}'
    companyName: '{companyName}'
    monthlyPrice: '{monthlyPrice}'
    cardType: '{cardType}'
    cardNumber: '{cardNumber}'
    renewalDate: '{renewalDate}'
  sampleData: []
  mocks:

barclaycard-leads-email:
  markdown: true
  path: Business-Services/Barclaycard/barclaycard-leads-email.md
  context: []
  sampleData: []
  mocks:

ease-accounting-leads-email:
  markdown: true
  path: Business-Services/EaseAccounting/ease-accounting-leads-email.md
  context: []
  sampleData: []
  mocks:

google-workspace-leads-email:
  markdown: true
  path: Business-Services/GoogleWorkspace/google-workspace-leads-email.md
  context: []
  sampleData: []
  mocks:

hsbc-leads-email:
  markdown: true
  path: Business-Services/HSBC/hsbc-leads-email.md
  context: []
  sampleData: []
  mocks:

mazuma-leads-email:
  markdown: true
  path: Business-Services/Mazuma/mazuma-leads-email.md
  context: []
  sampleData: []
  mocks:

taxassist-leads-email:
  markdown: true
  path: Business-Services/Taxassist/taxassist-leads-email.md
  context: []
  sampleData: []
  mocks:

100012:
  markdown: true
  path: Digital-Mailroom/100012.md
  context: []
  sampleData: []
  mocks:
    postItem:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

100013:
  markdown: true
  path: Digital-Mailroom/100013.md
  context:
    companyId: '{companyId}'
  sampleData: []
  mocks:
    service:
      class: \EmailModule\Mock\ServiceMock
    serviceView:
      class: \EmailModule\Mock\ServiceMock
    postItem:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

mailscan-extra-quota-charge-failed:
  markdown: true
  path: Digital-Mailroom/mailscan-extra-quota-charge-failed.md
  context: []
  sampleData: []
  mocks:
    postItem:
      class: \EmailModule\Mock\CompanyMock
    customer:
      class: \EmailModule\Mock\CustomerMock

mailscan-extra-quota-charged:
  markdown: true
  path: Digital-Mailroom/mailscan-extra-quota-charged.md
  context: []
  sampleData: []
  mocks:
    customer:
      class: \EmailModule\Mock\CustomerMock
    postItem:
      class: \EmailModule\Mock\CompanyMock


mailscan-no-mail-forwarding-service:
  markdown: true
  path: Digital-Mailroom/mailscan-no-mail-forwarding-service.md
  context: []
  sampleData: []
  mocks:
    customer:
      class: \EmailModule\Mock\CustomerMock
    postItem:
      class: \EmailModule\Mock\CompanyMock

mail-released-scan:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/mail-released-scan.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

mail-released-post:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/mail-released-post.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

mail-released-collect:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/mail-released-collect.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

mail-no-service:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/mail-no-service.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

mail-service-overdue-and-no-check-id:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/mail-service-overdue-and-no-check-id.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

mail-service-overdue:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/mail-service-overdue.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

mail-waiting-payment-scan:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/mail-waiting-payment-scan.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

parcel-no-service:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/parcel-no-service.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

parcel-released-collect-reminder:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/parcel-released-collect-reminder.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

parcel-released-collect:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/parcel-released-collect.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

parcel-service-overdue:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/parcel-service-overdue.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

parcel-waiting-payment-post-reminder:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/parcel-waiting-payment-post-reminder.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

parcel-waiting-payment-post:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/parcel-waiting-payment-post.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

unpaid-charge:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/unpaid-charge.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

submission-queue-ch-extra-fee-reminder:
  markdown: false
  path: Customer/Id-Check/submission-queue-ch-extra-fee-reminder.latte
  context:
    customerName: '{customerName}'
    companyName: '{companyName}'
    payLink: '{payLink}'
  sampleData: []
  mocks:

submission-queue-ch-extra-fee-wholesale-reminder:
  markdown: false
  path: Customer/Id-Check/submission-queue-ch-extra-fee-wholesale-reminder.latte
  context:
    customerName: '{customerName}'
    companyName: '{companyName}'
    payLink: '{payLink}'
  sampleData: []
  mocks:

upsellLayout:
  markdown: false
  path: Incorporation/Upsell/upsellLayout.latte
  context:
    emailTitle: '{emailTitle}'
  sampleData: []
  mocks:

notify-failed-direct-debit-charges-to-admins:
  markdown: false
  path: Direct-Debit/notify-failed-direct-debit-charges-to-admins.latte
  context: []
  mocks:

mailscan-service-mail-forwarding-forward:
  markdown: true
  path: Digital-Mailroom/mailscan-service-mail-forwarding-forward.md
  context: []
  sampleData: []
  mocks:
    company:
      class: \EmailModule\Mock\CompanyMock
    postItem:
      class: \EmailModule\Mock\PostItemMock
    forwardingAddress:
      class: \EmailModule\Mock\ForwardingAddressMock

parcel-rts:
  markdown: true
  path: Digital-Mailroom/Mailbox-Emails/parcel-rts.md
  context: []
  sampleData: []
  mocks:
    emailData:
      class: \EmailModule\Mock\EmailMock

revolut_template:
  markdown: false
  path: /Banking/Revolut/revolut_template.html
  context:
    name: '{Name}'
    email: '{Email}'
  sampleData: []
  mocks:
