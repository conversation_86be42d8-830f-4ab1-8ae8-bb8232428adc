<?php

namespace BusinessServicesModule\Responses;

use BusinessServicesModule\Responses\Response;

class FailureResponse extends Response
{
    public function __construct(?string $body = null, ?int $code = null, array $header = [])
    {
        $this->setHeader($header);
        $this->setBody($body);
        $this->setCode($code);
    }

    function isSuccess(): bool
    {
        return false;
    }
}