<?php

namespace BusinessServicesModule\Responses;

abstract class Response
{
    /**
     * @var array
     */
    private $header;

    /**
     * @var string|null
     */
    private $body;

    /**
     * @var int|null
     */
    private $code;

    public function getHeader(): array
    {
        return $this->header;
    }

    public function setHeader(array $header): void
    {
        $this->header = $header;
    }

    public function getBody(): string
    {
        return $this->body ?? 'n/a';
    }

    public function setBody(?string $body): void
    {
        $this->body = $body;
    }

    public function getCode(): int
    {
        return $this->code ?? $this->isSuccess() ? 200 : 400;
    }

    public function setCode(?int $code): void
    {
        $this->code = $code;
    }

    abstract function isSuccess(): bool;

}