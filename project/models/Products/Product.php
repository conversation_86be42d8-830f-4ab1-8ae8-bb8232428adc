<?php

namespace Models\Products;

class Product extends BasketProduct
{
    public const PRODUCT_FRAUD_PROTECTION = 201;
    public const PRODUCT_BUSINESS_TOOLKIT = 1350;
    public const PRODUCT_RENEWAL_SERVICE_ADDRESS = 806;
    public const PRODUCT_RENEWAL_REGISTERED_OFFICE = 334;
    public const PRODUCT_GUARANTEED_SAME_DAY = 589;
    public const PRODUCT_REGISTERED_OFFICE = 165;
    public const PRODUCT_REGISTERED_OFFICE_SERVICE_ADDRESS_BUNDLE = 1681;
    public const PRODUCT_REGISTER_OFFICE_EC1_N1_RENEWAL = 1643;
    public const PRODUCT_REGISTERED_OFFICE_SERVICE = 1257;
    public const PRODUCT_REGISTERED_OFFICE_N1_MONTHLY = 'registered_office_n1_monthly';
    public const PRODUCT_REGISTERED_OFFICE_N_1_MONTHLY_RENEWAL_FEE = 'registered_office_n1_monthly_renewal_fee';
    public const PRODUCT_REGISTERED_OFFICE_BASKET_UPSELL = 'registered_office_basket_upsell';
    public const PRODUCT_MAILBOX_STANDARD_INITIAL = 'mailbox_standard_initial_monthly';
    public const PRODUCT_MAILBOX_STANDARD_RENEWAL = 'mailbox_standard_renewal_monthly';
    public const PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH = 'mailbox_standard_privacy_initial_1_month';
    public const PRODUCT_MAILBOX_STANDARD_INITIAL_3_MONTHS = 'mailbox_standard_initial_3_months';
    public const PRODUCT_MAILBOX_PREMIUM_INITIAL = 'mailbox_premium_initial_monthly';
    public const PRODUCT_MAILBOX_PREMIUM_RENEWAL = 'mailbox_premium_renewal_monthly';
    public const PRODUCT_MAILBOX_PREMIUM_INITIAL_12_MONTHS = 'mailbox_premium_initial_12_months';
    public const PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL = 'mailbox_business_address_initial_monthly';
    public const PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL_UPSELL = 'mailbox_business_address_initial_upsell';
    public const PRODUCT_MAILBOX_BUSINESS_ADDRESS_RENEWAL = 'mailbox_business_address_renewal_monthly';
    public const MAILBOX_POST_ITEM_HANDLING_FEE = 'mailbox-post-item-handling-fee';
    public const MAILBOX_POST_ITEM_FORWARDING_FEE = 'mailbox-post-item-forwarding-fee';
    public const MAILBOX_PAY_TO_RELEASE_PRODUCT = 'mailbox-pay-to-release-product';
    public const PRODUCTS_THAT_HAVE_MAILBOX_SETTINGS = [
        self::PRODUCT_REGISTERED_OFFICE,
        self::PRODUCT_MAILBOX_STANDARD_INITIAL,
        self::PRODUCT_MAILBOX_PREMIUM_INITIAL,
        self::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL,
    ];
    public const PRODUCT_SERVICE_ADDRESS = 475;
    public const PRODUCT_PSC_REGISTER_UPSELL = 1729;
    public const PRODUCT_PSC_REGISTER = 1705;
    public const PRODUCT_PSC_REGISTER_CONFIRMATION_STATEMENT = 1706;
    public const PRODUCT_CUSTOMER_CREDIT = 609;
    public const SOLE_TRADER_START_UP_PACK_PLUS = 955;
    public const SOLE_TRADER_START_UP_PACK = 921;
    public const VAT_REGISTRATION = 299;
    public const PRODUCT_CSMS_REPORT = 'product_csms_report';
    public const PRODUCT_10_COMPANY_REPORTS = 'product_10_company_reports';
    public const PRODUCT_50_COMPANY_REPORTS = 'product_50_company_reports';
    public const PRODUCT_UNLIMITED_COMPANY_REPORTS = 'product_unlimited_company_reports';
    public const PRODUCT_UNLIMITED_COMPANY_REPORTS_MONTHLY = 'product_unlimited_company_reports_monthly';
    public const PRODUCT_20_WORKHUB_PASSES = 'product_20_workhub_passes';
    public const PRODUCT_DORMANT_COMPANY_ACCOUNTS = 306;
    public const PRODUCT_DORMANT_COMPANY_ACCOUNTS_EXPRESS = 882;
    public const PRODUCT_COMPANY_PROTECTION = 'company_protection_product';
    public const PRODUCT_COMPANY_PROTECTION_ANNUAL_RENEWAL = 'company_protection_annual_renewal_product';
    public const PRODUCT_MONTHLY_MAIL_FORWARDING_SERVICE = 'mail-forwarding-monthly-quota-service';
    public const PRODUCT_QUOTA_MAIL_FORWARDING_TIER_1 = 'quota_mail_forwarding_monthly_recurrence_tier_1';
    public const PRODUCT_QUOTA_MAIL_FORWARDING_TIER_2 = 'quota_mail_forwarding_monthly_recurrence_tier_2';
    public const PRODUCT_QUOTA_MAIL_FORWARDING_TIER_3 = 'quota_mail_forwarding_monthly_recurrence_tier_3';
    public const PRODUCT_QUOTA_MAIL_FORWARDING_RENEWAL_TIER_1 = 'quota_mail_forwarding_renewal_recurrence_tier_1';
    public const PRODUCT_QUOTA_MAIL_FORWARDING_RENEWAL_TIER_2 = 'quota_mail_forwarding_renewal_recurrence_tier_2';
    public const PRODUCT_QUOTA_MAIL_FORWARDING_RENEWAL_TIER_3 = 'quota_mail_forwarding_renewal_recurrence_tier_3';
    public const PRODUCT_EXTRA_QUOTA_MAIL_FORWARDING_SERVICE = 'mail_forwarding_extra_quota';
    public const MAIL_FORWARDING_ONE_YEAR_PRODUCT = 445;
    public const MAIL_FORWARDING_ADDRESS_N1_THREE_MONTHS_PRODUCT = 443;
    public const MAIL_FORWARDING_2015_PRODUCT = 1625;
    public const MAIL_FORWARDING_PRODUCT = 1270;
    public const MAIL_FORWARDING_TELEPHONE_ANSWERING_PRODUCT = 1271;
    public const MAIL_FORWARDING_3_MONTHS_PRODUCT = 443;
    public const MAIL_FORWARDING_1_MONTH_PRODUCT = 1783;
    public const PRODUCT_CH_EXTRA_FEE_FOR_COMPANY_INCORPORATION = 'ch_extra_fee_for_company_incorporation';
    public const PRODUCT_CH_EXTRA_FEE_FOR_CONFIRMATION_STATEMENT = 'ch_extra_fee_for_confirmation_statement';
    public const PRODUCT_REGISTRATION_REVIEW = 'registration-review';
    public const PRODUCT_SHELF_COMPANY_2024_PREMIUM = 'product_shelf_company_2024_premium';
    public const PRODUCT_SHELF_COMPANY_2024_STANDARD = 'product_shelf_company_2024_standard';
    public const PRODUCT_SHELF_COMPANY_2023_PREMIUM = 'product_shelf_company_2023_premium';
    public const PRODUCT_SHELF_COMPANY_2023_STANDARD = 'product_shelf_company_2023_standard';
    public const PRODUCT_TRANSFER_SHARES_CONFIRMATION_STATEMENT_1 = 'transfer_of_shares_confirmation_statement_one';
    public const PRODUCT_TRANSFER_SHARES_CONFIRMATION_STATEMENT_MULTIPLE = 'transfer_of_shares_confirmation_statement_multiple';
    public const PRODUCT_TRANSFER_SHARES_CONFIRMATION_STATEMENT_4_5 = 'transfer_of_shares_confirmation_statement_four_five';
    public const PRODUCT_ISSUE_SHARES_CONFIRMATION_STATEMENT_1 = 'issue_of_shares_confirmation_statement_one';
    public const PRODUCT_ISSUE_SHARES_CONFIRMATION_STATEMENT_MULTIPLE = 'issue_of_shares_confirmation_statement_multiple';
    public const PRODUCT_ISSUE_SHARES_CONFIRMATION_STATEMENT_4_5 = 'issue_of_shares_confirmation_statement_four_five';
    public const PRODUCT_COMPANY_DISSOLUTION = 309;
    public const PRODUCT_COMPANY_DISSOLUTION_EXPRESS = 1750;
    public const PRODUCT_CANCELLATION_PROTECTION = 'product_cancellation_protection';
    public const COMPANY_STAMP = 331;
    public const COMPANY_SEAL = 302;
    public const ALL_IN_ONE_PACK = 567;

    public const DUPLICATE_CERTIFICATE_OF_INCORPORATION = 318;

    public const MAIL_FORWARDING_STANDALONE_PRODUCTS = [
        self::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL,
        self::PRODUCT_MAILBOX_BUSINESS_ADDRESS_RENEWAL,
        self::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL_UPSELL
    ];

    /**
     * @var \DateTime
     */
    private $serviceDtStart;

    public static function hasMailboxSettings(?string $getNodeName)
    {
        return in_array($getNodeName, self::PRODUCTS_THAT_HAVE_MAILBOX_SETTINGS);
    }

    public function hasServiceDtStart()
    {
        return (bool) $this->serviceDtStart;
    }

    /**
     * @return \DateTime|null
     */
    public function getServiceDtStart(): ?\DateTime
    {
        return $this->serviceDtStart;
    }

    /**
     * @param \DateTime $serviceDtStart
     */
    public function setServiceDtStart(\DateTime $serviceDtStart)
    {
        $this->serviceDtStart = $serviceDtStart;
    }
}
