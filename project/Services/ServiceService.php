<?php

namespace Services;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\QueryException;
use Entities\Company;
use Entities\Customer;
use Entities\Order;
use Entities\OrderItem;
use Entities\ServiceSettings;
use Exceptions\Technical\OrderRefundException;
use Models\Products\Product;
use Repositories\ServiceRepository;
use Entities\Service;
use DataGrid\src\DataGrid\Doctrine\DoctrineDataSource;
use Entities\Customer as CustomerEntity;
use Libs\Exceptions\EntityNotFound;
use Utils\Date;
use Utils\NetteSmartObject;

class ServiceService extends NetteSmartObject
{
    const MONITORED_PRODUCT_IDS = [
        Product::PRODUCT_COMPANY_PROTECTION,
        Product::PRODUCT_COMPANY_PROTECTION_ANNUAL_RENEWAL
    ];

    /**
     * @var ServiceRepository
     */
    private $repository;

    /**
     * @var NodeService
     */
    private $nodeService;

    public function __construct(ServiceRepository $repository, NodeService $nodeService)
    {
        $this->repository = $repository;
        $this->nodeService = $nodeService;
    }

    /**
     * @param int $id
     * @return Service
     */
    public function getServiceById($id)
    {
        return $this->repository->find($id);
    }

    /**
     * @param array $ids
     * @return Service[]
     */
    public function getServicesByIds(array $ids = [])
    {
        return $this->repository->getServicesByIds($ids);
    }

    /**
     * @param Service $entity
     */
    public function deleteService(Service $entity)
    {
        $this->repository->removeEntity($entity);
    }

    /**
     * @param Service $entity
     */
    public function saveService(Service $entity)
    {
        $this->repository->saveEntity($entity);
    }

    /**
     * @param Service $service
     */
    public function flush(Service $service = null)
    {
        $this->repository->flush($service);
    }

    /**
     * @param Company $company
     * @param $serviceTypeId
     * @param Service $service (if provided this service will be ignored)
     * @return Service
     */
    public function getLastService(Company $company, $serviceTypeId, Service $service = null)
    {
        return $this->repository->getLastService($company, $serviceTypeId, $service);
    }

    /**
     * @return DoctrineDataSource
     */
    public function getListDatasource()
    {
        return new DoctrineDataSource($this->repository->getListBuilder());
    }

    /**
     * @param Company $company
     * @return DoctrineDataSource
     */
    public function getListDatasourceForCompany(Company $company)
    {
        return new DoctrineDataSource($this->repository->getListBuilder($company));
    }

    public function getServiceListForCompany(Company $company)
    {
        return $this->repository->getListBuilder($company)->getQuery()->getResult();
    }

    /**
     * @param CustomerEntity $customer
     * @param $serviceId
     * @return Service
     * @throws EntityNotFound
     */
    public function getCustomerService(CustomerEntity $customer, $serviceId)
    {
        $service = $this->getServiceById($serviceId);
        if (!$service) {
            throw new EntityNotFound("Service $serviceId doesn't exist");
        }
        $serviceCustomer = $service->getCompany()->getCustomer();
        if (!$serviceCustomer->isEqual($customer)) {
            throw new EntityNotFound("Service $serviceId doesn't exist");
        }
        return $service;
    }

    /**
     * @param OrderItem $orderItem
     * @return Service
     * @throws OrderRefundException
     */
    public function getOrderItemService(OrderItem $orderItem)
    {
        $service = $this->repository->getServiceByOrderItem($orderItem);
        if ($service) {
            return $service;
        }

        // S546 - kept old behaviour to handle old services without orderItemId
        $services = $this->repository->getOrderItemServices($orderItem);
        if ($services) {
            if (count($services) > 1) {
                // case one order: 2x SA for different companies
                foreach ($services as $service) {
                    $orderCompany = $orderItem->getCompany();
                    if ($orderCompany && $orderCompany->isEqual($service->getCompany())) {
                        return $service;
                    }
                }

                throw OrderRefundException::multipleServicesDetected($orderItem);

            } else {
                return $services[0];
            }
        }
    }

    /**
     * @param Order $order
     * @return array
     */
    public function getOrderServices(Order $order): array
    {
        return $this->repository->getOrderServices($order);
    }

    /**
     * @param int $productId
     * @return IterableResult
     */
    public function getServicesByProductId($productId)
    {
        return $this->repository->getServicesByProductId($productId);
    }

    /**
     * @param Date $from
     * @param Date $to
     * @param string $event
     * @return IterableResult
     */
    public function getServicesDataExpiringWithinDatesForEvent(Date $from, Date $to, $event)
    {
        return $this->repository->getServicesDataExpiringWithinDatesForEvent($from, $to, $event);
    }

    /**
     * @param Date $from
     * @param Date $to
     * @param string $event
     * @return IterableResult
     */
    public function getAllServicesDataExpiringWithinDatesForEvent(
        Date $from,
        Date $to,
        $event,
        bool $enabledForMonthlyServices = true
    ) {
        return $this->repository->getAllServicesDataExpiringWithinDatesForEvent(
            $from,
            $to,
            $event,
            $enabledForMonthlyServices
        );
    }

    /**
     * @return IterableResult
     */
    public function getRefundedServices()
    {
        return $this->repository->getRefundedServices();
    }

    /**
     * @param Service $service
     * @param Date|DateTime $dtStart
     * @param Date|DateTime $dtExpires
     */
    public function setServiceDates(Service $service, $dtStart, $dtExpires)
    {
        if ($dtStart > $dtExpires) {
            throw new \InvalidArgumentException('The expire date must be after the start date.');
        }

        $service->setDtStart($dtStart->setTime(0, 0, 0));
        $service->setDtExpires($dtExpires->setTime(0, 0, 0));
        $this->repository->flush($service);
    }

    /**
     * @param Company $company
     * @param DateTime $date
     * @return Service[]|ArrayCollection
     */
    public function getCompanyNonExpiredServicesOn(Company $company, DateTime $date)
    {
        return $company->getNonExpiredServicesOn($date);
    }

    /**
     * @param Service $service
     * @return bool
     */
    public function isFormationServiceForCompany(Service $service)
    {
        $company = $service->getCompany();
        $serviceOrder = $service->getOrder();
        $companyOrder = $company->getOrder();

        if (!$serviceOrder || !$companyOrder) { /** @phpstan-ignore-line */
            return false;
        }

        if ($serviceOrder->isEqual($companyOrder)) {
            return true;
        }

        if (!$company->isIncorporated()) {
            return false;
        }

        $orderDate = Date::createFromDateTime($service->getOrder()->getDtc());
        return $orderDate < $company->getIncorporationDate();
    }

    /**
     * @param Service $service
     */
    public function markAsDowngraded(Service $service)
    {
        $service->setStateId(Service::STATE_DOWNGRADED);
        foreach ($service->getChildren() as $childService) {
            $childService->setStateId(Service::STATE_DOWNGRADED);
            $this->repository->flush($childService);
        }
        $this->repository->flush($service);
    }

    /**
     * @param Service $service
     */
    public function markAsUpgraded(Service $service)
    {
        $service->setStateId(Service::STATE_UPGRADED);
        foreach ($service->getChildren() as $childService) {
            $childService->setStateId(Service::STATE_UPGRADED);
            $this->repository->flush($childService);
        }
        $this->repository->flush($service);
    }

    public function markAsDisabled(Service $service): void
    {
        $service->setStateId(Service::STATE_DISABLED);
        $this->saveService($service);
    }

    /**
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getLatestServiceBySettings(ServiceSettings $settings): Service
    {
        return $this->repository->getLatestServiceBySettings($settings);
    }

    /**
     * @param Company $company
     * @param $serviceTypeId
     * @return Service[]
     */
    public function getCompanyServicesByType(Company $company, $serviceTypeId)
    {
        return $this->repository->getCompanyServicesByType($company, $serviceTypeId);
    }

    /**
     * @param array $dates
     * @return Service[]
     */
    public function getServicesToSendRemindersFor(array $dates): array
    {
        return $this->repository->getServicesToSendRemindersFor($dates);
    }

    /**
     * @param array $dates
     * @param array $productIds
     * @return Service[]
     */
    public function getServicesExpiringWithinDays(array $dates, array $productIds = [], string $eventKey = null): array
    {
        return $this->repository->getServicesExpiringWithinDays($dates, $productIds, $eventKey);
    }

    /**
     * @return Service[]
     */
    public function getActiveMonitoringServices(): array
    {
        return $this->repository->getActiveMonitoringServices($this->getMonitoredProductIds());
    }

    /**
     * @param int $companyId
     * @return bool
     */
    public function hasActiveMonitoringService(int $companyId): bool
    {
        return $this->repository->hasActiveMonitoringService($companyId, $this->getMonitoredProductIds());
    }

    private function getMonitoredProductIds(): array
    {
        foreach (self::MONITORED_PRODUCT_IDS as $productId) {
            $monitoredProductIds[] = $this->nodeService->requiredProductByName($productId)->getId();
        }

        return $monitoredProductIds ?? [];
    }

    /**
     * @throws QueryException
     */
    public function getFullPrivacyProductsToUpdateRenewal(int $firstYearProductId, int $secondYearProductId): iterable
    {
        return $this->repository->getFullPrivacyProductsToUpdateRenewal($firstYearProductId, $secondYearProductId);
    }
}
