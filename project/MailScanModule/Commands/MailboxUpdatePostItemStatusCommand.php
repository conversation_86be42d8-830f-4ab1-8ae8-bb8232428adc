<?php

declare(strict_types=1);

namespace MailScanModule\Commands;

use Doctrine\Common\Collections\Collection;
use MailScanModule\ApiClient\IMailroomApiClient;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Dto\PostItemBag;
use MailScanModule\Emailers\MailboxEmailer;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Providers\MailboxUpdateStatusProvider;
use Psr\Log\LoggerInterface;
use Services\CompanyService;

class MailboxUpdatePostItemStatusCommand
{
    private const DEFAULT_CHUNK_SIZE = 500;
    private const MAX_CHUNKS = 1000;
    private const DEBUG_KEY_TOTAL = 'total';
    private const DEBUG_KEY_UNCHANGED = 'unchanged';
    private const DEBUG_KEY_ERRORS = 'companies_with_errors';
    const DEBUG_KEY_EMAIL_LOG = 'email_log';
    const DEBUG_KEY_COMPANY_NOT_FOUND = 'company_not_found';

    protected array $updatingTotals;
    private bool $dryRun;
    private bool $debug;
    private int $chunkSize;

    public function __construct(
        private readonly IMailroomApiClient $mailroomApiClient,
        private readonly LoggerInterface $logger,
        private readonly CompanyService $companyService,
        private readonly MailboxEmailer $emailer,
        private readonly MailboxUpdateStatusProvider $updateMailboxStatusProvider,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function updatePostItemStatus(
        bool $dryRun = true,
        bool $debug = false,
        int $chunkSize = self::DEFAULT_CHUNK_SIZE,
    ): array {
        $this->dryRun = $dryRun;
        $this->debug = $debug;
        $this->chunkSize = $chunkSize;
        $this->updatingTotals = [self::DEBUG_KEY_EMAIL_LOG => []];

        $this->logger->info('Initiating MailboxUpdatePostItemStatus command...', [
            'dryRun' => $dryRun,
            'debug' => $debug,
            'chunkSize' => $this->chunkSize
        ]);

        $chunk = 1;
        $lastId = null;

        do {
            $this->logger->info("Processing chunk {$chunk}...", ['lastId' => $lastId]);

            try {
                $itemBag = $this->mailroomApiClient->getRequireUpdatePostItems(
                    $this->chunkSize,
                    $lastId
                );
            } catch (\Exception $e) {
                $this->logger->error('Failed to fetch post items', [
                    'chunk' => $chunk,
                    'lastId' => $lastId,
                    'error' => $e->getMessage()
                ]);
                break;
            }

            $itemCount = $itemBag->countAllPostItems();
            $this->updateUpdatingTotals(self::DEBUG_KEY_TOTAL, $itemCount);

            $this->logger->info("Chunk {$chunk} fetched", [
                'chunkItems' => $itemCount,
                'totalProcessed' => $this->updatingTotals[self::DEBUG_KEY_TOTAL],
            ]);

            if ($itemCount === 0) {
                $this->logger->info('Empty chunk, finishing processing.');
                break;
            }

            $newLastId = $itemBag->getLastItemId();

            if ($newLastId === $lastId && $newLastId !== null) {
                $this->logger->error('Same ID returned, breaking to prevent infinite loop', [
                    'chunk' => $chunk,
                    'lastId' => $lastId
                ]);
                break;
            }

            $lastId = $newLastId;

            if (is_null($lastId)) {
                $this->logger->info('No more post items to process.');
                break;
            }

            $this->logger->info(sprintf('Updating %s items...', $itemCount));

            $itemsPerCompany = $this->sortItemsByCompanies($itemBag);

            /** @var MailroomPostItemData[] $items */
            foreach ($itemsPerCompany as $companyNumber => $items) {
                try {
                    $this->updateCompanyPostItemsStatus((string) $companyNumber, $items);
                } catch (\Throwable $e) {
                    $this->logger->error($e->getMessage());
                    $this->updateUpdatingTotals(self::DEBUG_KEY_ERRORS, count($items));
                }
                $this->resetDoctrine();
            }

            $this->logProgress(
                "Chunk {$chunk} processed successfully",
                [
                    'companiesProcessed' => count($itemsPerCompany),
                    'updatingTotals' => json_encode(
                        $this->updatingTotals,
                        JSON_PRETTY_PRINT
                    )
                ]
            );

            $chunk++;

            if ($chunk > self::MAX_CHUNKS) {
                $this->logger->warning('Reached maximum chunk limit, stopping processing.', [
                    'maxChunks' => self::MAX_CHUNKS,
                    'updatingTotals' => $this->updatingTotals[self::DEBUG_KEY_TOTAL]
                ]);
                break;
            }

            $this->performMemoryCleanup();
        } while (true);

        $this->logger->info(sprintf(
            'Updating totals: %s',
            json_encode(
                $this->updatingTotals,
                JSON_PRETTY_PRINT
            )
        ));

        $this->logger->info('Finished MailboxUpdatePostItemStatus command.', [
            'dryRun' => $dryRun,
            'debug' => $debug,
        ]);

        return $this->updatingTotals;
    }

    private function sortItemsByCompanies(PostItemBag $itemBag): array
    {
        $this->logger->info('Separating items by customer...');

        $companies = $itemBag->getCompaniesInBag();
        $itemsPerCompany = [];

        foreach ($companies as $companyNumber) {
            $itemsPerCompany[$companyNumber] = $itemBag->getCompanyPostItems($companyNumber);
        }

        $this->logProgress(
            'Items per company separated',
            [
                'total companies' => count($itemsPerCompany),
                'total items' => $itemBag->countAllPostItems(),
                'items per company' => json_encode(
                    array_map(fn ($items) => count($items), $itemsPerCompany),
                    JSON_PRETTY_PRINT
                )
            ],
            true
        );

        return $itemsPerCompany;
    }

    private function logProgress(string $message, ?array $data, ?bool $suppressLog = false): void
    {
        if ($suppressLog && !$this->debug) {
            return;
        }

        $this->logger->info(
            sprintf('%s - %s', $message, json_encode($data))
        );
    }

    private function updateUpdatingTotals(string $key, int $itemsCount = 1, bool $emailLog = false): void
    {
        if ($emailLog) {
            $this->updatingTotals[self::DEBUG_KEY_EMAIL_LOG][$key] = ($this->updatingTotals[self::DEBUG_KEY_EMAIL_LOG][$key] ?? 0) + $itemsCount;
            return;
        }

        $this->updatingTotals[$key] = ($this->updatingTotals[$key] ?? 0) + $itemsCount;
    }

    /**
     * @throws \Exception
     */
    private function updateCompanyPostItemsStatus(string $companyNumber, array $items): void
    {
        $company = $this->companyService->getCompanyByCompanyNumber($companyNumber);

        if (is_null($company)) {
            $this->logProgress(
                'Company not found',
                [
                    'company number' => $companyNumber,
                ],
            );
            $this->updateUpdatingTotals(self::DEBUG_KEY_COMPANY_NOT_FOUND, count($items));
            return;
        }

        $this->logProgress(
            'Updating post items statuses for company',
            [
                'company number' => $companyNumber,
                'company name' => $company->getName(),
                'items count' => count($items),
            ],
        );

        /* @var MailroomPostItemData $postItem */
        foreach ($items as $postItem) {
            $this->logProgress(
                'Updating item',
                $postItem->jsonSerialize(),
                true
            );

            $newStatus = $this->updateMailboxStatusProvider->getNewStatus($postItem);

            if (!is_null($newStatus)) {
                $this->logProgress(
                    'Updating item status',
                    [
                        'company number' => $companyNumber,
                        'post item id' => $postItem->getId(),
                        'old status' => $postItem->getStatus(),
                        'new status' => $newStatus,
                    ],
                );

                $this->updateUpdatingTotals(sprintf('%s>%s', $postItem->getStatus(), $newStatus));
                $postItem->setDesiredStatus($newStatus);
            } else {
                $this->logProgress(
                    'Item status unchanged',
                    [
                        'company number' => $companyNumber,
                        'post item id' => $postItem->getId(),
                        'status' => $postItem->getStatus(),
                    ],
                    true
                );

                $this->updateUpdatingTotals(self::DEBUG_KEY_UNCHANGED);
            }
        }

        if (!$this->dryRun) {
            $this->logProgress(
                'Updating post items status on mailroom',
                []
            );

            $this->mailroomApiClient->setPostItemStatus($items);
        }

        $emailsSent = $this->emailer->sendOneEmailForEachItem(
            $company,
            $items,
            MailboxEmailer::PROCESSING_STEP_UPDATE,
            $this->dryRun
        );

        foreach ($emailsSent as $emailLog) {
            $this->updateUpdatingTotals(
                key: $emailLog['email_name'],
                emailLog: true
            );
        }
    }

    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    private function performMemoryCleanup(): void
    {
        $this->resetDoctrine();
        gc_collect_cycles();
        $this->logger->info('Memory cleanup performed', [
            'memory' => $this->formatBytes(memory_get_usage(true)),
            'peak' => $this->formatBytes(memory_get_peak_usage(true))
        ]);
    }

    private function resetDoctrine(): void
    {
        $this->companyService->clearDoctrineMemory();
    }
}
