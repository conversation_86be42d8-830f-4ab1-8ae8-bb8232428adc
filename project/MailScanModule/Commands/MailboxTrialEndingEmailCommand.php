<?php

declare(strict_types=1);

namespace MailScanModule\Commands;

use Entities\Service;
use Exceptions\Technical\NodeException;
use MailScanModule\Emailers\MailboxEmailer;
use Models\Products\Product;
use Monolog\Logger;
use Services\EventService;
use Services\NodeService;
use Services\ServiceService;

class MailboxTrialEndingEmailCommand
{
    private const DAYS_BEFORE = 7;
    private const EVENT_KEY = 'mailbox.trial_ending_email';
    private const MAILBOX_TRIAL_PRODUCT_NAMES = [
        Product::PRODUCT_MAILBOX_STANDARD_INITIAL_3_MONTHS,
        Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH,
    ];

    public function __construct(
        readonly ServiceService $serviceService,
        readonly NodeService $nodeService,
        readonly Logger $logger,
        readonly MailboxEmailer $mailboxEmailer,
        readonly EventService $eventService
    ) {
    }

    /**
     * @throws NodeException
     * @throws \Exception
     */
    public function sendTrialEndingEmails(
        int $daysBefore = self::DAYS_BEFORE,
        bool $dryRun = false,
        bool $debug = false,
    ): void {
        $this->logDebug($debug, "Starting trial ending email process", [
            'daysBefore' => $daysBefore,
            'dryRun' => $dryRun,
            'debug' => $debug,
        ]);

        $productIds = array_map(
            fn ($productName) => $this->nodeService->requiredProductByName($productName)->getId(),
            self::MAILBOX_TRIAL_PRODUCT_NAMES
        );

        $this->logDebug($debug, "Retrieved product IDs", [
            'productIds' => $productIds,
            'productNames' => self::MAILBOX_TRIAL_PRODUCT_NAMES,
        ]);

        $services = $this->getExpiringServices($daysBefore, $productIds, $debug);

        $this->logDebug($debug, "Found expiring services", [
            'count' => count($services),
            'serviceIds' => array_map(fn ($s) => $s->getId(), $services),
        ]);

        $this->processServices($services, $debug, $dryRun);
    }

    /**
     * @throws \Exception
     */
    private function getExpiringServices(int $daysBefore, array $productIds, bool $debug): array
    {
        try {
            $services = $this->serviceService->getServicesExpiringWithinDays(
                [$daysBefore],
                $productIds,
                self::EVENT_KEY
            );

            $this->logDebug($debug, "Services query executed", [
                'daysBefore' => $daysBefore,
                'productIds' => $productIds,
                'eventKey' => self::EVENT_KEY,
                'resultCount' => count($services),
            ]);

            return $services;
        } catch (\Exception $e) {
            $this->logger->error("Failed to retrieve expiring services", [
                'daysBefore' => $daysBefore,
                'productIds' => $productIds,
                'error' => $e->getMessage(),
                'exception' => $e,
            ]);

            throw $e;
        }
    }

    private function processServices(array $services, bool $debug, bool $dryRun): void
    {
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        foreach ($services as $service) {
            $serviceId = $service->getId();

            $this->logDebug($debug, "Processing service", [
                'serviceId' => $serviceId,
                'customerId' => $service->getCustomer()->getId(),
                'expiresAt' => $service->getDtExpires()->format('Y-m-d H:i:s'),
            ]);

            try {
                $emailData = $this->prepareEmailData($service, $debug);

                if (!$emailData) {
                    $skippedCount++;
                    continue;
                }

                $emailLog = null;
                if (!$dryRun) {
                    $emailLog = $this->sendEmail($emailData, $debug, );
                }

                if ($emailLog && !$dryRun) {
                    $this->logDebug($debug, "Email sent successfully", [
                        'serviceId' => $serviceId,
                        'emailLogId' => $emailLog->getId() ?? 'N/A',
                    ]);

                    $this->recordEvent($serviceId, $debug);
                    $successCount++;
                } else {
                    $errorCount++;
                    $this->logger->error("Email sending failed", [
                        'serviceId' => $serviceId,
                    ]);
                }
            } catch (\Exception $e) {
                $errorCount++;
                $this->logger->error("Failed to process service", [
                    'serviceId' => $serviceId,
                    'error' => $e->getMessage(),
                    'exception' => $e,
                ]);

                continue;
            }
        }

        $this->logProcessingSummary($successCount, $errorCount, $skippedCount, $debug);
    }

    private function prepareEmailData(Service $service, bool $debug): ?array
    {
        try {
            $customer = $service->getCustomer();
            $product = $service->getProduct();
            $renewalProduct = $service->getRenewalProduct();
            $corePackageService = $service->getCompany()->getActiveCorePackageService();

            $emailData = [
                'customer' => $customer,
                'firstName' => $customer->getFirstName(),
                'mailboxProductName' => Service::$types[$product->getServiceTypeId()],
                'trialPeriod' => $this->getParsedDuration($service->getInitialDuration()),
                'endDate' => $service->getDtExpires(),
                'renewalPrice' => $renewalProduct->getPrice(),
                'packageName' => Service::$types[$corePackageService->getServiceTypeId()],
            ];

            $this->logDebug($debug, "Email data prepared", [
                'serviceId' => $service->getId(),
                'customerId' => $customer->getId(),
                'customerEmail' => $customer->getEmail(),
                'firstName' => $emailData['firstName'],
                'mailboxProductName' => $emailData['mailboxProductName'],
                'trialPeriod' => $emailData['trialPeriod'],
                'endDate' => $emailData['endDate']->format('Y-m-d H:i:s'),
                'renewalPrice' => $emailData['renewalPrice'],
                'packageName' => $emailData['packageName'],
            ]);

            return $emailData;
        } catch (\Exception $e) {
            $this->logger->error("Failed to prepare email data", [
                'serviceId' => $service->getId(),
                'error' => $e->getMessage(),
                'exception' => $e,
            ]);

            return null;
        }
    }

    private function sendEmail(array $emailData, bool $debug): mixed
    {
        try {
            $this->logDebug($debug, "Attempting to send email", [
                'recipientEmail' => $emailData['customer']->getEmail(),
                'firstName' => $emailData['firstName'],
            ]);

            $emailLog = $this->mailboxEmailer->sendTrialEndingEmail($emailData);

            $this->logDebug($debug, "Email sending completed", [
                'emailLogId' => $emailLog->getId(),
            ]);

            return $emailLog;
        } catch (\Exception $e) {
            $this->logger->error("Email sending failed", [
                'recipientEmail' => $emailData['customer']->getEmail(),
                'error' => $e->getMessage(),
                'exception' => $e,
            ]);

            throw $e;
        }
    }

    private function recordEvent(int $serviceId, bool $debug): void
    {
        try {
            $this->logDebug($debug, "Recording event", [
                'serviceId' => $serviceId,
                'eventKey' => self::EVENT_KEY,
            ]);

            $this->eventService->notify(self::EVENT_KEY, $serviceId);

            $this->logDebug($debug, "Event recorded successfully", [
                'serviceId' => $serviceId,
            ]);
        } catch (\Exception $e) {
            $this->logger->error("Failed to record event", [
                'serviceId' => $serviceId,
                'eventKey' => self::EVENT_KEY,
                'error' => $e->getMessage(),
                'exception' => $e,
            ]);

            throw $e;
        }
    }

    private function logProcessingSummary(int $successCount, int $errorCount, int $skippedCount, bool $debug): void
    {
        $totalProcessed = $successCount + $errorCount + $skippedCount;

        $summaryData = [
            'totalProcessed' => $totalProcessed,
            'successful' => $successCount,
            'errors' => $errorCount,
            'skipped' => $skippedCount,
            'successRate' => $totalProcessed > 0 ? round(($successCount / $totalProcessed) * 100, 2) : 0,
        ];

        if ($errorCount > 0 || $skippedCount > 0) {
            $this->logger->warning("Trial ending email processing completed with issues", $summaryData);
        } else {
            $this->logger->info("Trial ending email processing completed successfully", $summaryData);
        }

        $this->logDebug($debug, "Processing summary details", $summaryData);
    }

    private function logDebug(bool $debug, string $message, array $context = []): void
    {
        if ($debug) {
            $this->logger->debug($message, $context);
        }
    }

    private function getParsedDuration(string $duration): string
    {
        return match ($duration) {
            Service::DURATION_ONE_MONTH => '1 month',
            Service::DURATION_THREE_MONTHS => '3 months',
            Service::DURATION_ONE_YEAR => '12 months',
            default => throw new \InvalidArgumentException('Invalid duration'),
        };
    }
}
