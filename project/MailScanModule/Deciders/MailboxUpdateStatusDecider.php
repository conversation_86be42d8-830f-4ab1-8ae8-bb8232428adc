<?php

declare(strict_types=1);

namespace MailScanModule\Deciders;

use MailScanModule\Dto\MailroomPostItemData;

class MailboxUpdateStatusDecider
{
    private const MAIL_DESTROY_TIME = '3 months';
    private const PARCEL_DESTROY_TIME = '7 days';
    private const MAIL_COLLECT_TIME_LIMIT = '7 days';

    public function shouldBeSecurelyDestroyed(MailroomPostItemData $postItem): bool
    {
        if ($postItem->isParcel()) {
            return $this->hasExceededTimeLimit($postItem, self::PARCEL_DESTROY_TIME);
        }

        return $this->hasExceededTimeLimit($postItem, self::MAIL_DESTROY_TIME);
    }

    public function shouldBeScanOnly(MailroomPostItemData $postItem): bool
    {
        return !$postItem->isParcel()
            && $postItem->isStatusToBeCollected()
            && ($this->hasExceededTimeLimit($postItem, self::MAIL_COLLECT_TIME_LIMIT));
    }

    private function getDateOnly(\DateTime $date): \DateTime
    {
        return $date->setTime(0, 0, 0);
    }

    private function hasExceededTimeLimit(MailroomPostItemData $postItem, string $modifier): bool
    {
        return $this->getDateOnly(new \DateTime()) > $this->getDateOnly((clone $postItem->getDtc())->modify($modifier));
    }
}
