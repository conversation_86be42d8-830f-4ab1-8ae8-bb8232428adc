<?php

declare(strict_types=1);

namespace MailScanModule\Admin\View;

use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use CompanyModule\Facades\MailForwardingFacade;
use CompanyModule\Facades\PostItemHandlingFacade;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use Exceptions\Technical\NodeException;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Exceptions\InvalidForwardingAddressException;
use MailScanModule\Exceptions\NotAMailboxProductException;
use MailScanModule\Facades\MailForwardingAddressFacade;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Services\PostItemService;

readonly class MailForwardingAdminView
{

    public function __construct(
        private MailboxTierDecider          $mailboxTierDecider,
        private PostItemHandlingFacade      $postItemHandlingFacade,
        private MailForwardingAddressFacade $mailForwardingAddressFacade,
        private PostItemService             $postItemService,
        private MailForwardingFacade        $mailForwardingFacade,
    ) {
    }

    /**
     * @throws NotAMailboxProductException
     * @throws NodeException
     * @throws NonUniqueResultException
     * @throws InvalidForwardingAddressException
     */
    public function getMailForwardingData(Company $company): array
    {
        $mailboxService = $company->getActiveOrLatestMailboxService();
        if (is_null($mailboxService)) {
            return [];
        }

        $initialProduct = $this->postItemService->getProductByTier($this->mailboxTierDecider->determineMailboxTier($mailboxService), $company);

        $handlingSettings = $this->postItemHandlingFacade->getPostItemHandlingSetting($company);
        $address = $this->mailForwardingAddressFacade->getForwardingAddress($company);

        $mailSettings = $handlingSettings->getParsedHandlingSetting(PostItemHandlingSetting::KEY_POST_ITEM_HANDLING);
        $parcelSettings = $handlingSettings->getParsedHandlingSetting(PostItemHandlingSetting::KEY_PARCEL_HANDLING);

        $tierName = $this->mailboxTierDecider->getTierName($mailboxService);

        $maxNonStatutoryQuota = MailboxProductPropertyHelper::getMaximumQuotaByTypeAndProcessingMethod(
            $initialProduct,
            MailboxProductPropertyHelper::TYPE_NON_STATUTORY,
            $handlingSettings->getHandlingSettingByType(PostItemHandlingSetting::KEY_POST_ITEM_HANDLING)
        );

        $remainingNonStatutory = $maxNonStatutoryQuota - $this->mailForwardingFacade->getQuotasByType(
            $company,
            'collect',
            PostItemTypeEnum::TYPE_NON_STATUTORY->value
        );

        $maxForwardedPostsQuota = MailboxProductPropertyHelper::getMaximumGenericQuotaByTypeAndProcessingMethod(
            $initialProduct,
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
        );

        $remainingForwardedPosts = $maxForwardedPostsQuota - $this->mailForwardingFacade->getQuotasByType(
            $company,
            'post'
        );

        return [
            'mailbox_package' => $this->getMailboxType($company),
            'mailbox_tier_name' => $tierName,
            'mail_settings' => $mailSettings,
            'parcel_acceptance' => MailboxProductPropertyHelper::getAcceptanceByType(
                $initialProduct,
                MailboxProductPropertyHelper::TYPE_PARCEL
            ),
            'parcel_settings' => $parcelSettings,
            'email_for_scanned_items' => $company->getCustomerEmail(),
            'remaining_non_statutory' => $remainingNonStatutory,
            'max_non_statutory_quota' => $maxNonStatutoryQuota,
            'remaining_forwarded_posts' => $remainingForwardedPosts,
            'max_forwarded_posts_quota' => $maxForwardedPostsQuota,
            'renewal_date' => $mailboxService->getDtExpires(),
            'mf_address' => [
                'address1' => $address['address1'],
                'address2' => $address['address2'],
                'address3' => $address['address3'],
                'town' => $address['town'],
                'country' => $address['country'],
                'postcode' => $address['postcode'],
            ],
        ];
    }

    private function getMailboxType(Company $company): array
    {
        $service = $company->getActiveOrLatestMailboxService();

        if (is_null($service)) {
            return ['name' => 'None', 'is_active' => false];
        }

        $isActive = $service->isActive() && !$service->isOverdueAndExpired();

        try {
            $tierName = str_replace('Mailbox ', '', $this->mailboxTierDecider->getTierName($service));
        } catch (\Exception $e) {
            return ['name' => 'None', 'is_active' => false];
        }

        return [
            'name' => "$tierName ({$service->getServiceName()})",
            'is_active' => $isActive,
        ];
    }

}
