formation_product_id	mailbox_product_name	post_item_type	handling_format	within_quota	charging_attempts	id_check_complete	service_overdue	company_name	post_status
1313	null	statutory	scanned	true	null	true	false	STAT_NO-SERVICE	added
1313	null	non-statutory	scanned	true	null	true	false	NON-STAT_NO-SERVICE	added
1316	mailbox_standard_renewal_monthly	statutory	collect	true	null	true	false	STAT_STD_COLLECT	added
1316	mailbox_standard_renewal_monthly	non-statutory	collect	true	null	true	false	NON-STAT_STD_COLLECT	added
1317	mailbox_premium_renewal_monthly	statutory	post	true	null	true	false	STAT_PREM_POST	added
1317	mailbox_premium_renewal_monthly	non-statutory	post	true	null	true	false	STAT_PREM_POST	added
1317	mailbox_premium_renewal_monthly	non-statutory	post	false	null	true	false	NON-STAT_PREM_POST_NO-QUOTAS	added
1317	mailbox_premium_renewal_monthly	statutory	post	true	null	true	false	STAT_PREM_POST_INVALID-ADDRESS	added
1313	165	statutory	scanned	true	null	true	false	STAT_RO_SCAN	added
1313	165	non-statutory	scanned	false	null	true	false	NON-STAT_RO_SCAN_NO-QUOTAS	added
1317	mailbox_premium_renewal_monthly	statutory	scanned	true	null	true	true	STAT_OVERDUE	added
1317	mailbox_premium_renewal_monthly	non-statutory	scanned	true	null	true	true	NON-STAT_OVERDUE	added
1317	mailbox_premium_renewal_monthly	statutory	scanned	true	null	false	true	STAT_OVERDUE_NO-CHECK-ID	added
1317	mailbox_premium_renewal_monthly	non-statutory	scanned	true	null	false	true	NON-STAT_OVERDUE_NO-CHECK-ID	added
1317	mailbox_premium_renewal_monthly	parcel	collect	true	null	true	true	PARCEL_OVERDUE_NO-CHECK-ID	added
1313	165	non-statutory	scanned	true	null	true	false	NON-STAT_RO_WAITING-PAYMENT	added
1313	null	statutory	scanned	true	null	false	false	STAT_NO-SERVICE_NO-CHECK-ID	added
1313	null	parcel	collect	true	null	false	false	PARCEL_NO-SERVICE_NO-CHECK-ID	added
1313	null	parcel	collect	true	null	true	false	PARCEL_NO-SERVICE	added
1317	mailbox_premium_renewal_monthly	parcel	collect	true	null	true	true	PARCEL_SERVICE-OVERDUE	added
1313	165	parcel	collect	true	null	true	true	PARCEL_SERVICE-OVERDUE_LITE	added
1316	mailbox_standard_renewal_monthly	parcel	collect	true	null	true	false	PARCEL_STD_COLLECT	added
1316	mailbox_standard_renewal_monthly	parcel	collect	true	null	true	false	PARCEL_STD_COLLECT_3DAYS	added
1313	165	parcel	collect	true	null	true	false	PARCEL_LITE	added
1317	mailbox_premium_renewal_monthly	parcel	post	true	null	true	false	PARCEL_PREM_POST	added
1317	mailbox_premium_renewal_monthly	parcel	post	true	null	true	false	PARCEL_PREM_POST_3DAYS	added
1317	mailbox_premium_renewal_monthly	parcel	collect	false	4	true	false	PARCEL_PREM_WITH-CHARGING-ATTEMPTS	added
1313	null	statutory	scanned	true	null	false	false	STAT_NO-SERVICE_NO-CHECK-ID	added
1313	null	parcel	collect	true	null	false	false	PARCEL_NO-SERVICE_NO-CHECK-ID	added
1313	165	statutory	scanned	true	null	false	false	STAT_NO-CHECK-ID	added
1317	mailbox_premium_renewal_monthly	parcel	collect	true	null	false	false	PARCEL_NO-CHECK-ID	added
1316	mailbox_standard_renewal_monthly	non-statutory	scanned	false	null	true	false	NON-STAT_STD_NO-QUOTAS	added