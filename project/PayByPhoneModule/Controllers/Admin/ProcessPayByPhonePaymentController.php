<?php

declare(strict_types=1);

namespace PayByPhoneModule\Controllers\Admin;

use FirebaseModule\Facades\FirebaseAuthFacade;
use Framework\FUser;
use OmnipayModule\Helpers\AmountHelper;
use OmnipayModule\Helpers\RequestHelper;
use PayByPhoneModule\Services\PayByPhoneService;
use RouterModule\Domain\FlashMessage;
use RouterModule\Generators\IUrlGenerator;
use RouterModule\Helpers\IControllerHelper;
use Services\CustomerService;
use Services\UserService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

readonly class ProcessPayByPhonePaymentController
{
    public const PAY_BY_PHONE_NODE_ID = 1258;

    public function __construct(
        private IRenderer $renderer,
        private IControllerHelper $controllerHelper,
        private PayByPhoneService $payByPhoneService,
        private UserService $userService,
        private CustomerService $customerService,
        private Request $request,
        private FirebaseAuthFacade $firebaseAuthFacade,
        private string $omnipayUrl,
        private ?string $omnipayLocalComponentUrl,
        private string $stripePublicKey,
    ) {
    }

    public function processPayByPhone(string $email, ?string $phone = null): Response
    {
        try {
            $basket = $this->payByPhoneService->getPhonePaymentBasket();

            if (empty($basket->getItems())) {
                throw new \InvalidArgumentException('No item to process. Please select one package/product to process.');
            }

            $basketHasPackage = !empty(array_filter($basket->getItems(), fn ($item) => $item->isPackage()));

            foreach ($basket->getItems() as $item) {
                if ($item->requiredCompanyNumber && !$basketHasPackage) {
                    throw new \InvalidArgumentException('You have selected items that require a package. Please review your request.');
                }

                if ($item->requiredIncorporatedCompanyNumber && !$item->getCompanyId()) {
                    throw new \InvalidArgumentException('You have selected items that requires a company to be associated. Please review your request.');
                }
            }

            $customer = $this->customerService->getCustomerByEmail($email);

            /* @phpstan-ignore-next-line */
            if (empty($customer)) {
                $customer = $this->firebaseAuthFacade->createCustomer(['email' => $email, 'phone' => $phone]);
            }

            $this->payByPhoneService->setIsPayByPhone(true);
            $this->payByPhoneService->setPayByPhoneCustomerId($customer->getCustomerId());

            $price = $basket->getPrice(null, true, $customer);

            return $this->renderer->render([
                'agent' => $this->userService->getUserById(FUser::getSignedIn()->getId()), /* @phpstan-ignore-line */
                'customer' => $customer,
                'origin' => 'pay_by_phone',
                'basket' => $basket,
                'serializedBasket' => $basket->getSerializedBasket($customer),
                'clientIp' => $this->request->getClientIp(),
                'baseUrl' => substr_replace($this->controllerHelper->url('homepage', [], IUrlGenerator::SECURE | IUrlGenerator::ABSOLUTE), '', -1),
                'acceptHeader' => $this->request->headers->get('Accept'),
                'omnipayToken' => RequestHelper::generateToken('payment', (string) $customer->getId()),
                'omnipaySalt' => 'cms_customer_id',
                'omnipayUri' => 'payment',
                'omnipayUrl' => $this->omnipayLocalComponentUrl ?? $this->omnipayUrl,
                'stripePublicKey' => $this->stripePublicKey,
                'price' => $price,
                'totalInCents' => AmountHelper::convertToCents((float) $price->total),
            ]);
        } catch (\Exception $e) {
            $this->controllerHelper->notify(FlashMessage::error($e->getMessage()));

            return $this->controllerHelper->redirectionTo(self::PAY_BY_PHONE_NODE_ID, [], IUrlGenerator::ADMIN_LINK);
        }
    }
}
