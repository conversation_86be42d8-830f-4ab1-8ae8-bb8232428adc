<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers\Api;

use CompaniesHouseModule\Repositories\CompanyIncorporationRepository;
use CompanyFormationModule\Facades\PrintedCertificateFacade;
use CompanyFormationModule\Repositories\SicCodesRepository;
use CompanyIncorporationModule\Services\BusinessBankingService;
use CompanyIncorporationModule\Services\MailForwardingService;
use CompanyIncorporationModule\Services\RegisteredOfficeService;
use CompanyIncorporationModule\Services\StepUrlService;
use CompanyIncorporationModule\Validators\AddressValidator;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;
use Models\Products\Product;
use PaymentModule\Factories\InlinePaymentFactory;
use Repositories\Nodes\PackageRepository;
use RouterModule\ApiController;
use RouterModule\Generators\UrlGenerator;
use Services\CompanyService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Utils\Helpers\ArrayHelper;

class CompanyFormationStepApiController extends ApiController
{
    public const ENGLISH_KEYBOARD_VALIDATION_FIELDS = [
        'premise', 'street', 'thoroughfare', 'post_town', 'county', 'postcode', 'country',
    ];
    public const REQUIRED_VALIDATION_FIELDS = [
        'premise'   => 'Please provide Building name/number',
        'street'    => 'Please provide Street',
        'post_town' => 'Please provide town',
        'postcode'  => 'Please provide postcode',
        'country'   => 'Please provide country',
    ];
    public const MAX_ALLOWED_SIC_CODES = 4;
    private const POSTCODE_FIELD = 'postcode';

    public function __construct(
        private readonly UrlGenerator $urlGenerator,
        private readonly CompanyService $companyService,
        private readonly PrintedCertificateFacade $printedCertificateFacade,
        private readonly InlinePaymentFactory $inlinePaymentFactory,
        private readonly SicCodesRepository $sicCodesRepository,
        private readonly CompanyIncorporationRepository $companyIncorporationRepository,
        private readonly BusinessBankingService $businessBankingService,
        private readonly MailForwardingService $mailForwardingService,
        private readonly RegisteredOfficeService $registeredOfficeService,
        private readonly PackageRepository $packageRepository,
    ) {
        $this->setApikeyRequiredFalse();
    }

    public function registeredOffice(Company $company): JsonResponse
    {
        try {
            /** @var Product $fullPrivacyPackageMonthly */
            $fullPrivacyPackageMonthly = $this->registeredOfficeService->getFullPrivacyPackageMonthly();

            $this->handleMSGRegisteredOfficeWithEmptyPostcode($company, $company->getIncorporationFormSubmission());

            return $this->apiSuccessResponse([
                'registeredOfficeUpsellOffer' => $this->registeredOfficeService->canShowUpsellOffer($company),
                'inlinePayment' => $this->inlinePaymentFactory->create(
                    $fullPrivacyPackageMonthly,
                    $company,
                    ['retryWithANewCard' => true]
                ),
                'prefillAddress' => $this->registeredOfficeService->getPrefillAddresses(
                    $company,
                    $company->getIncorporationFormSubmission()->getAddresses()
                ),
                'addressFields' => $company->getIncorporationFormSubmission()->getRegisteredOfficeAddress()->getFields(),
                'registeredOfficeUpsellId' => $fullPrivacyPackageMonthly->getId(),
                'registeredOfficeUpsellType' => $company->getSettings()->getRegisteredOfficeUpsell()->getType()
                    ?: $fullPrivacyPackageMonthly->getId(),
                'registeredEmailAddressValue' => $company->getIncorporationFormSubmission()->getRegisteredEmailAddress(),
                'canShowNewConfirmationStatementFields' => !empty($company->getCustomer()->getId()),
                'msgAddress' => $this->registeredOfficeService->getMSGAddress(),
                'hasMSGRegisteredOffice' => $company->getIncorporationFormSubmission()->getMsgAddress(),
                'hasRegisteredOfficeId' => !empty($company->getRegisteredOfficeId()),
                'canShowCertificatePrintedQuestion' => $this->canShowCertificatePrintedQuestion($company),
                'certificatePrinted' => $this->printedCertificateFacade->getSetting($company)?->isEnabled(),
                'isWholesale' => $company->getCustomer()->isWholesale(),
            ]);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function industryType(Company $company): JsonResponse
    {
        try {
            return $this->apiSuccessResponse([
                'sicCodes' => $this->sicCodesRepository->getCHSicCodes(),
                'companySicCodes' => $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company),
            ]);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function saveRegisteredOffice(Company $company, ?string $registeredOfficeFormData = null): JsonResponse
    {
        $errors = [];
        try {
            $data = json_decode($registeredOfficeFormData, true);
            $useOwnAddress = $data['registeredOfficeUpsellType'] === RegisteredOfficeService::USE_OWN_ADDRESS;

            /** @var CompanyIncorporation $incorporation */
            $incorporation = $company->getIncorporationFormSubmission();
            if ($useOwnAddress) {
                $errors = $this->validateRegisteredOfficeForm($data, $company);
            }

            if (empty($data['registeredEmailAddress'])) {
                $errors['registeredEmailAddress'] = ['Please provide an email'];
            }

            if (!$company->getRegisteredOfficeId()
                && $data['registeredOfficeUpsellType'] != RegisteredOfficeService::USE_OWN_ADDRESS
            ) {
                $errors['registeredOfficeUpsellType'] = [
                    'You need to pay for the package before using Companies Made Simple Privacy Services.',
                ];
            }

            if (!empty($errors)) {
                throw new \InvalidArgumentException('Validations Failed');
            }

            $incorporation = $this->registeredOfficeService->processRegisteredOffice($data, $company, $incorporation);

            return $this->apiSuccessResponse([
                'message' => 'Data saved successfully.',
                'addressFields' => $incorporation->getRegisteredOfficeAddress()->getFields(),
                'hasRegisteredOfficeId' => !empty($company->getRegisteredOfficeId()),
                'registeredOfficeUpsellOffer' => $this->registeredOfficeService->canShowUpsellOffer($company),
                'hasMSGRegisteredOffice' => $incorporation->getMsgAddress(),
                'companyAddress' => $this->registeredOfficeService->getCompanyAddress($incorporation),
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, additionalData: $errors, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e, $errors);
        }
    }

    public function addSicCode(Company $company, ?string $codeData = null): JsonResponse
    {
        try {
            $data = json_decode($codeData, true);
            $code = ArrayHelper::get($data, 'code', null);

            if (!$code) {
                throw new \InvalidArgumentException('No valid code was submitted');
            }

            $companySicCodes = $company->getSicCodes();

            if (in_array($code, $companySicCodes)) {
                throw new \InvalidArgumentException('SIC code already added to this company');
            }

            if (count($companySicCodes) >= self::MAX_ALLOWED_SIC_CODES) {
                throw new \InvalidArgumentException('You already have 4 SIC codes associated to your company');
            }

            $companySicCodes[] = $code;
            $company->setSicCodes($companySicCodes);
            $this->companyService->saveCompany($company);
            $this->updateIncorporationFormSicCodes($company, $companySicCodes);

            return $this->apiSuccessResponse([
                'message' => 'SIC code saved successfully.',
                'companySicCodes' => $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company),
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function removeSicCode(Company $company, ?string $codeData = null): JsonResponse
    {
        try {
            $data = json_decode($codeData, true);
            $code = ArrayHelper::get($data, 'code', null);

            if (!$code) {
                throw new \InvalidArgumentException('No valid code was submitted.');
            }

            $this->sicCodesRepository->remove($company->getId(), $code);

            $this->updateIncorporationFormSicCodes($company, $company->getSicCodes());

            return $this->apiSuccessResponse([
                'message'  => 'SIC code removed successfully.',
                'companySicCodes' => $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company),
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function saveBusinessBankingOffer(Company $company, ?string $selectedOffers): JsonResponse
    {
        try {
            $offers = json_decode($selectedOffers, true) ?? [];

            if (!is_array($offers)) {
                throw new \InvalidArgumentException('Validations Failed: offers - No offers selected');
            }

            $this->businessBankingService->saveOffers($company, $offers);

            return $this->apiSuccessResponse([
                'message'  => 'Offers saved successfully.',
                'redirect_url' => $this->urlGenerator->url(
                    StepUrlService::APPOINTMENTS_PAGE,
                    ['company' => $company->getId()]
                ),
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function processMailForwardingEvent(Company $company, ?string $mailForwardingEvent): JsonResponse
    {
        try {
            $data = json_decode($mailForwardingEvent, true);
            if (empty($data)) {
                throw new \InvalidArgumentException('Validations Failed: event - No event data provided.');
            }

            $this->mailForwardingService->processEvent($company, $data);

            return $this->apiSuccessResponse([
                'message' => 'Event processed successfully.',
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function retrieveMailForwardingData(Company $company): JsonResponse
    {
        try {
            if (!$mailForwardingOffer = $this->mailForwardingService->createOffer($company)) {
                throw new \InvalidArgumentException('Error retrieving the offer.');
            }

            $inlinePayment = ArrayHelper::get($mailForwardingOffer, 'inlinePayment', null);

            if (is_null($inlinePayment)) {
                throw new \InvalidArgumentException('Inline payment error.');
            }

            return $this->apiSuccessResponse([
                'omnipayComponentData' => $inlinePayment->getOmnipayComponentData(),
                'omnipayUrl' => $inlinePayment->getOmnipayUrl(),
                'paymentStatus' => $inlinePayment->getStatus(),
                'isOnInitRetryNewCard' => $inlinePayment->isOnFailureRetryWithANewCard() && $inlinePayment->doesntHaveTokens(),
                'product' => ArrayHelper::get($mailForwardingOffer, 'product', null),
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    private function handleMSGRegisteredOfficeWithEmptyPostcode(
        Company $company,
        CompanyIncorporation $incorporation,
    ): void {
        if ($company->getRegisteredOfficeId() && empty($incorporation->getRegisteredOfficeAddress()->getPostcode())) {
            $this->registeredOfficeService->setMSGRegisteredOffice($incorporation, true);
        }
    }

    private function validateRegisteredOfficeForm(array $data, Company $company): array
    {
        $errors = [];
        foreach ($data as $field => $value) {
            if (in_array($field, self::ENGLISH_KEYBOARD_VALIDATION_FIELDS)) {
                $validation = AddressValidator::checkEnglishKeyboard($value);
                if (!empty($validation)) {
                    $errors[$field][] = $validation;
                }
            }

            if (array_key_exists($field, self::REQUIRED_VALIDATION_FIELDS) && AddressValidator::fieldEmpty($field, $value)) {
                $errors[$field][] = self::REQUIRED_VALIDATION_FIELDS[$field];
            }

            if (array_key_exists($field, self::REQUIRED_VALIDATION_FIELDS)) {
                $validation = AddressValidator::checkLength($field, $value);
                if (!empty($validation)) {
                    $errors[$field][] = $validation;
                }
            }

            if ($field !== self::POSTCODE_FIELD) {
                continue;
            }

            $postCodeValidation = AddressValidator::validatePostCode($value, $company);
            if (!empty($postCodeValidation)) {
                $errors['postcode'][] = $postCodeValidation;
            }
        }

        $countryValidation = AddressValidator::validateCountryCode($data['country']);
        if (!empty($data['country']) && !empty($countryValidation)) {
            $errors['country'][] = $countryValidation;
        }

        return $errors;
    }

    private function updateIncorporationFormSicCodes(Company $company, array $companySicCodes): void
    {
        $incorporation = $company->getIncorporationFormSubmission();
        $incorporation->setSicCodes($companySicCodes);
        $this->companyIncorporationRepository->saveEntity($incorporation);
    }

    private function canShowCertificatePrintedQuestion(Company $company): bool
    {
        $package = $this->packageRepository->getPackageById($company->getProductId());

        if (!$package) {
            return false;
        }

        return $package->hasPrintedCertificateOptionEnabled(); /* @phpstan-ignore-line */
    }
}
