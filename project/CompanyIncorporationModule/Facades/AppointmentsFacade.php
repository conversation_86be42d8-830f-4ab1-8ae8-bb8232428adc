<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Facades;

use CompaniesHouseModule\Dto\NatureOfControlsData;
use CompaniesHouseModule\Entities\Address;
use CompaniesHouseModule\Entities\CorporateDetails;
use CompaniesHouseModule\Entities\CorporateName;
use CompaniesHouseModule\Entities\NatureOfControl;
use CompaniesHouseModule\Entities\PersonalDetails;
use CompanyFormationModule\Dto\PscsData;
use CompanyFormationModule\Entities\IMember;
use CompanyFormationModule\Entities\Shares;
use CompanyFormationModule\Facades\PscRegisterSettingFacade;
use CompanyFormationModule\Factories\NatureOfControlsFactory;
use CompanyIncorporationModule\Controllers\Api\CompanyFormationStepApiController;
use CompanyIncorporationModule\Dto\ConsolidatedMember;
use CompanyIncorporationModule\Exceptions\ExistingMemberException;
use CompanyIncorporationModule\Exceptions\InvalidNatureOfControlException;
use CompanyIncorporationModule\Exceptions\MemberNotFoundException;
use CompanyIncorporationModule\Exceptions\MemberNotImplementedException;
use CompanyIncorporationModule\Factories\ConsolidatedMemberFactory;
use CompanyIncorporationModule\Helpers\MemberHashHelper;
use CompanyIncorporationModule\Services\MemberService;
use CompanyIncorporationModule\Services\StepUrlService;
use CompanyIncorporationModule\Validators\AddressValidator;
use CompanyIncorporationModule\Validators\AuthenticationValidator;
use CompanyIncorporationModule\Validators\DateValidator;
use CompanyIncorporationModule\Validators\NatureOfControlValidator;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission;
use Entities\CompanyHouse\Helper\Authentication;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Utils\Date;
use Utils\Helpers\ArrayHelper;

class AppointmentsFacade
{
    public const ROLE_MAP = [
        IMember::TYPE_DIRECTOR => [
            'getter' => 'getDirector',
            'createPerson' => 'createDirectorPerson',
            'createCorporate' => 'createDirectorCorporate',
        ],
        IMember::TYPE_SECRETARY => [
            'getter' => 'getSecretary',
            'createPerson' => 'createSecretaryPerson',
            'createCorporate' => 'createSecretaryCorporate',
        ],
        IMember::TYPE_PSC => [
            'getter' => 'getPsc',
            'createPerson' => 'createPscPerson',
            'createCorporate' => 'createPscCorporate',
        ],
        IMember::TYPE_SUBSCRIBER => [
            'getter' => 'getShareholders',
            'createPerson' => 'createShareholderPerson',
            'createCorporate' => 'createShareholderCorporate',
        ],
    ];

    public function __construct(
        private EntityManager $em,
        private MemberService $memberService,
        private MemberHashHelper $memberHashHelper,
        private NatureOfControlsFactory $natureOfControlsFactory,
        private PscRegisterSettingFacade $pscRegisterSettingFacade,
        private StepUrlService $stepUrlService,
        private ConsolidatedMemberFactory $consolidatedMemberFactory,
    ) {
    }

    /**
     * @throws MemberNotFoundException
     * @throws ORMException
     * @throws MemberNotImplementedException
     * @throws ExistingMemberException
     */
    public function saveMember(Company $company, array $membersData): ConsolidatedMember
    {
        $consolidatedMemberHash = ArrayHelper::get($membersData, 'consolidatedMemberHash', null);
        $consolidatedMember = null;

        $roles = ArrayHelper::get($membersData, 'roles', []);

        $details = $this->getDetails($membersData);

        if (empty($consolidatedMemberHash)) {
            $this->validateExistingMembers($company, $details);
        }

        if ($consolidatedMemberHash) {
            $consolidatedMember = $this->memberHashHelper->getConsolidatedMember($consolidatedMemberHash);
            $existingRoles = array_keys($this->memberService->getRoles($consolidatedMember, $company->getCompanyCategory(), false));
            $toDelete = array_diff($existingRoles, $roles);
            $this->deleteOfficers($toDelete, $consolidatedMember);
        }

        $addresses = $consolidatedMember ? $this->memberService->getAddresses($consolidatedMember) : null;

        $emptyAddress = new Address('', '');

        $this->removePscNoReason($company->getIncorporationFormSubmission(), $roles);

        return $this->createOfficers(
            $consolidatedMember,
            $details,
            $addresses && $addresses->serviceAddress ? $addresses->serviceAddress : $emptyAddress,
            $addresses && $addresses->residentialAddress ? $addresses->residentialAddress : $emptyAddress,
            $company->getIncorporationFormSubmission(),
            $roles
        );
    }

    /**
     * @throws MemberNotFoundException
     */
    public function deleteMember(
        Company $company,
        ConsolidatedMember $consolidatedMember,
        ?array $rolesToDelete = null,
    ): void {
        if ($company->getIncorporationFormSubmission()->getFormSubmissionId() !== $consolidatedMember->getFormSubmission()->getFormSubmissionId()) {
            throw new UnauthorizedHttpException('You are not authorized to delete this member.');
        }

        $this->memberService->deleteConsolidatedMember($consolidatedMember, $rolesToDelete);
    }

    /**
     * @throws MemberNotFoundException
     * @throws ORMException
     * @throws \Exception
     */
    public function saveMemberDetails(Company $company, ConsolidatedMember $consolidatedMember, array $membersData): array
    {
        if ($company->getIncorporationFormSubmission()->getFormSubmissionId() !== $consolidatedMember->getFormSubmission()->getFormSubmissionId()) {
            throw new UnauthorizedHttpException('You are not authorized to update this member.');
        }

        $consolidatedMember->setConsentToAct($membersData['consentToAct']);

        AuthenticationValidator::validate($membersData['authentication']);

        $authentication = new Authentication(
            $membersData['authentication']['birthTown'],
            $membersData['authentication']['telephone'],
            $membersData['authentication']['mumsMaidenName']
        );
        $consolidatedMember->setAuthentication($authentication, $company->getCompanyCategory());

        $this->persistOfficers($consolidatedMember->getMembers());

        return $authentication->jsonSerialize();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function saveShareholderInformation(
        Company $company,
        PersonalDetails|CorporateDetails $memberDetails,
        Authentication $authentication,
        Shares $allotmentOfShares,
        ?Address $residentialAddress,
        ?Address $serviceAddress,
    ): ConsolidatedMember {
        $emptyAddress = new Address('', '');
        $member = $this->createOfficers(
            null,
            $memberDetails,
            $serviceAddress ?? $emptyAddress,
            $residentialAddress ?? $emptyAddress,
            $company->getIncorporationFormSubmission(), ['SUB']
        );
        $member->getEmptyShareholder()->setShares($allotmentOfShares);
        $member->getEmptyShareholder()->setAuthentication($authentication);

        $this->persistOfficers($member->getMembers());

        return $member;
    }

    public function saveOfficerAddresses(array $members, Address $serviceAddress, ?Address $residentialAddress): void
    {
        foreach ($members as $member) {
            if (!$member instanceof IMember) {
                throw new \InvalidArgumentException('All elements in $members must be instances of IMember');
            }
            $this->assignAddressesToMember($member, $serviceAddress, $residentialAddress);
        }
        $this->persistOfficers($members);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws InvalidNatureOfControlException
     */
    public function saveNatureOfControl(
        Company $company,
        ConsolidatedMember $consolidatedMember,
        array $data,
        PscsData $pscsData,
    ): NatureOfControl {
        $natureOfControlData = NatureOfControlsData::default();

        if (isset($data['ownershipOfShares'])) {
            $natureOfControlData->setOwnershipOfShares($data['ownershipOfShares']);
        }

        if (isset($data['ownershipOfVotingRights'])) {
            $natureOfControlData->setOwnershipOfVotingRights($data['ownershipOfVotingRights']);
        }

        if (isset($data['rightToAppointAndRemoveDirectors'])) {
            $natureOfControlData->setRightToAppointAndRemoveDirectors($data['rightToAppointAndRemoveDirectors']);
        }

        if (isset($data['significantInfluenceOrControl'])) {
            $natureOfControlData->setSignificantInfluenceOrControl($data['significantInfluenceOrControl']);
        }

        $natureOfControl = $this->natureOfControlsFactory->create($natureOfControlData);
        NatureOfControlValidator::validate($natureOfControl, $company->getCompanyCategory());

        $consolidatedMember->setNatureOfControl($natureOfControl);
        $this->persistOfficers($consolidatedMember->getMembers());

        $this->savePscSetting($company, $pscsData);

        return $natureOfControl;
    }

    public function validateAddress(Address $address): array
    {
        $errors = [];
        $validation = AddressValidator::checkEnglishKeyboard($address->getPremise());
        if (!empty($validation)) {
            $errors['premise'][] = $validation;
        }
        $validation = AddressValidator::checkEnglishKeyboard($address->getStreet());
        if (!empty($validation)) {
            $errors['street'][] = $validation;
        }
        $validation = AddressValidator::checkEnglishKeyboard($address->getThoroughfare());
        if (!empty($validation)) {
            $errors['thoroughfare'][] = $validation;
        }
        $validation = AddressValidator::checkEnglishKeyboard($address->getPostTown());
        if (!empty($validation)) {
            $errors['post_town'][] = $validation;
        }
        $validation = AddressValidator::checkEnglishKeyboard($address->getCounty());
        if (!empty($validation)) {
            $errors['county'][] = $validation;
        }
        $validation = AddressValidator::checkEnglishKeyboard($address->getPostcode());
        if (!empty($validation)) {
            $errors['postcode'][] = $validation;
        }
        $validation = AddressValidator::checkEnglishKeyboard($address->getCountry());
        if (!empty($validation)) {
            $errors['country'][] = $validation;
        }

        if (AddressValidator::fieldEmpty('premise', $address->getPremise())) {
            $errors['premise'][] = CompanyFormationStepApiController::REQUIRED_VALIDATION_FIELDS['premise'];
        }
        if (AddressValidator::fieldEmpty('street', $address->getStreet())) {
            $errors['street'][] = CompanyFormationStepApiController::REQUIRED_VALIDATION_FIELDS['street'];
        }
        if (AddressValidator::fieldEmpty('post_town', $address->getPostTown())) {
            $errors['post_town'][] = CompanyFormationStepApiController::REQUIRED_VALIDATION_FIELDS['post_town'];
        }
        if (AddressValidator::fieldEmpty('postcode', $address->getPostcode())) {
            $errors['postcode'][] = CompanyFormationStepApiController::REQUIRED_VALIDATION_FIELDS['postcode'];
        }
        if (AddressValidator::fieldEmpty('country', $address->getCountry())) {
            $errors['country'][] = CompanyFormationStepApiController::REQUIRED_VALIDATION_FIELDS['country'];
        }

        $validation = AddressValidator::checkLength('premise', $address->getPremise());
        if (!empty($validation)) {
            $errors['premise'][] = $validation;
        }
        $validation = AddressValidator::checkLength('street', $address->getStreet());
        if (!empty($validation)) {
            $errors['street'][] = $validation;
        }
        $validation = AddressValidator::checkLength('post_town', $address->getPostTown());
        if (!empty($validation)) {
            $errors['post_town'][] = $validation;
        }
        $validation = AddressValidator::checkLength('postcode', $address->getPostcode());
        if (!empty($validation)) {
            $errors['postcode'][] = $validation;
        }
        $validation = AddressValidator::checkLength('country', $address->getCountry());
        if (!empty($validation)) {
            $errors['country'][] = $validation;
        }

        return $errors;
    }

    /**
     * @throws ORMException
     */
    public function deleteOfficers(array $roles, ConsolidatedMember $consolidatedMember): void
    {
        $deletedMembers = [];

        foreach ($roles as $role) {
            if (!isset(self::ROLE_MAP[$role])) {
                continue;
            }

            $getter = self::ROLE_MAP[$role]['getter'];
            $member = $consolidatedMember?->$getter(); /* @phpstan-ignore-line */

            if (is_array($member)) {
                foreach ($member as $m) {
                    $this->em->remove($m);
                    $deletedMembers[] = $m;
                }
                continue;
            }

            if ($member) {
                $this->em->remove($member);
                $deletedMembers[] = $member;
            }
        }

        $this->em->flush($deletedMembers);
    }

    public function isDuplicateMember(IMember $member, PersonalDetails|CorporateDetails $details): bool
    {
        if (($details instanceof CorporateDetails) !== $member->isCorporate()) {
            return false;
        }

        if ($details instanceof CorporateDetails) {
            return mb_strtoupper($member->getCorporateDetails()->getCorporateName()->getCompanyName()) === mb_strtoupper($details->getCorporateName()->getCompanyName()); /* @phpstan-ignore-line */
        }

        $personalDetails = $member->getPersonalDetails(); /* @phpstan-ignore-line */

        $dob = $personalDetails->getDob();
        $detailsDob = $details->getDob();

        if ($dob && $detailsDob) {
            return mb_strtoupper($personalDetails->getForename()) === mb_strtoupper($details->getForename())
                && mb_strtoupper($personalDetails->getSurname()) === mb_strtoupper($details->getSurname())
                && $dob->format('d/m/Y') === $detailsDob->format('d/m/Y');
        }

        return mb_strtoupper($personalDetails->getForename()) === mb_strtoupper($details->getForename())
            && mb_strtoupper($personalDetails->getSurname()) === mb_strtoupper($details->getSurname());
    }

    private function createOfficers(
        ?ConsolidatedMember $consolidatedMember,
        PersonalDetails|CorporateDetails $details,
        Address $address,
        Address $residentialAddress,
        FormSubmission $incorporation,
        array $roles,
    ): ConsolidatedMember {
        $members = [];
        $isCorporate = $details instanceof CorporateDetails;
        $setDetailsMethod = $isCorporate ? 'setCorporateDetails' : 'setPersonalDetails';

        foreach ($roles as $role) {
            $detailsCopy = clone $details;

            if (!isset(self::ROLE_MAP[$role])) {
                continue;
            }

            $getter = self::ROLE_MAP[$role]['getter'];
            $createMethod = $isCorporate
                ? self::ROLE_MAP[$role]['createCorporate']
                : self::ROLE_MAP[$role]['createPerson'];

            if ($isCorporate) {
                if ($role === IMember::TYPE_PSC) {
                    $detailsCopy->setIdentificationType(IMember::TYPE_PSC);
                }

                if (empty($detailsCopy->getPlaceRegistered())) {
                    $detailsCopy->setPlaceRegistered($detailsCopy->getCountryOrState());
                }
            }

            $member = $consolidatedMember?->$getter();

            if ($member) {
                if (is_array($member)) {
                    foreach ($member as $m) {
                        $m->$setDetailsMethod($detailsCopy);
                        $m->setAddress($address);
                        $members[] = $m;
                    }
                    continue;
                }

                $member->$setDetailsMethod($detailsCopy);
                $member->setAddress($address);
                $members[] = $member;
                continue;
            }

            $members[] = $this->memberService->$createMethod(
                $incorporation,
                $detailsCopy,
                $address,
                $isCorporate ? null : $residentialAddress
            );
        }

        $this->persistOfficers($members);

        return $this->consolidatedMemberFactory->fromMemberArray($members);
    }

    private function getDetails(array $membersData): PersonalDetails|CorporateDetails
    {
        $isCorporate = ArrayHelper::get($membersData, 'isCorporate');

        $forename = ArrayHelper::get($membersData, 'forename');
        $surname = ArrayHelper::get($membersData, 'surname');

        if ($isCorporate) {
            $corporateName = ArrayHelper::get($membersData, 'companyName');
            $identificationType = ArrayHelper::get($membersData, 'identificationType', null);
            $countryRegistered = ArrayHelper::get($membersData, 'countryRegistered', null);
            $registrationNumber = ArrayHelper::get($membersData, 'registrationNumber', null);
            $placeRegistered = ArrayHelper::get($membersData, 'placeRegistered', null);
            $governingLaw = ArrayHelper::get($membersData, 'governingLaw', null);
            $legalForm = ArrayHelper::get($membersData, 'legalForm', null);

            return new CorporateDetails(
                new CorporateName($forename, $surname, $corporateName),
                $identificationType,
                $placeRegistered,
                $registrationNumber,
                $governingLaw ?? null,
                $legalForm ?? null,
                $countryRegistered
            );
        }

        $title = ArrayHelper::get($membersData, 'title');
        $middleName = ArrayHelper::get($membersData, 'middleName', null);
        $nationality = ArrayHelper::get($membersData, 'nationality', null);
        $occupation = ArrayHelper::get($membersData, 'occupation', null);
        $countryOfResidence = ArrayHelper::get($membersData, 'countryOfResidence', null);
        $dob = ArrayHelper::get($membersData, 'dob', '');
        $dobDate = new Date($dob);
        DateValidator::validateDateOfBirth($dobDate);

        return new PersonalDetails(
            $forename,
            $surname,
            $dobDate,
            $nationality,
            $countryOfResidence,
            $occupation,
            $middleName,
            $title
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function persistOfficers(array $members): void
    {
        foreach ($members as $member) {
            $this->em->persist($member);
        }

        $this->em->flush($members);
    }

    private function assignAddressesToMember(IMember $member, Address $address, ?Address $residentialAddress): void
    {
        $member->setAddress($address); /* @phpstan-ignore-line */

        if (!$member->isCorporate()) {
            $member->setResidentialAddress($residentialAddress); /* @phpstan-ignore-line */
        }
    }

    private function savePscSetting(Company $company, PscsData $data)
    {
        $this->pscRegisterSettingFacade->saveSetting($company, $data);
    }

    /**
     * @throws MemberNotFoundException
     * @throws MemberNotImplementedException
     * @throws ExistingMemberException
     */
    private function validateExistingMembers(Company $company, PersonalDetails|CorporateDetails $details): void
    {
        foreach ($this->memberService->getConsolidatedMembers($company) as $consolidatedMember) {
            /** @var IMember $member */
            foreach ($consolidatedMember->getMembers() as $member) {
                if ($this->isDuplicateMember($member, $details)) {
                    throw new ExistingMemberException(\sprintf('An appointment with the same details already exists. Please <a href="%s">click here</a> to update it.', $this->stepUrlService->getEditMemberUrl($company->getId(), $this->memberHashHelper->getMemberHash($consolidatedMember), $consolidatedMember->isCorporate() ? ConsolidatedMember::TYPE_CORPORATE : ConsolidatedMember::TYPE_PERSON)));
                }
            }
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function removePscNoReason(FormSubmission\CompanyIncorporation $incorporation, array $roles): void
    {
        if (!is_null($incorporation->getNoPscReason()) && in_array('PSC', $roles)) {
            $incorporation->setNoPscReason(null);
            $this->em->persist($incorporation);
            $this->em->flush($incorporation);
        }
    }
}
