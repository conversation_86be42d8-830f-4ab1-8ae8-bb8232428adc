<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Services;

use CompaniesHouseModule\Entities\NatureOfControl;
use CompanyFormationModule\Entities\PscCandidate;
use CompanyFormationModule\Factories\PscSuggesterFactory;
use CompanyIncorporationModule\Dto\ConsolidatedMember;
use Entities\Company;

class PscService
{
    public function __construct(
        private PscSuggesterFactory $pscSuggesterFactory,
        private \CompanyFormationModule\Services\ShareholderService $shareholderService,
    ) {
    }

    public function getSuggestedNatureOfControl(Company $company, ?ConsolidatedMember $consolidatedMember): ?NatureOfControl
    {
        if ($company->getIncorporationFormSubmission()->isLlp()) {
            return null;
        }

        if (!$consolidatedMember) {
            return null;
        }

        if ($this->shareholderService->hasMultipleShareClasses($company)) {
            return null;
        }

        return $this->getSuggestedPscBasedOnConsolidatedMember($company, $consolidatedMember)?->getNatureOfControl();
    }

    public function getWarning(Company $company, ?ConsolidatedMember $consolidatedMember): ?string
    {
        if ($company->getIncorporationFormSubmission()->isLlp()) {
            return null;
        }

        if (!$consolidatedMember) {
            return null;
        }

        if ($this->shareholderService->hasMultipleShareClasses($company)) {
            return null;
        }

        $suggestedPsc = $this->getSuggestedPscBasedOnConsolidatedMember($company, $consolidatedMember);
        if (!$suggestedPsc) {
            if ($consolidatedMember->isPsc() && !$this->hasSpecialNatureOfControlSelected($consolidatedMember)) {
                return 'The system suggests this person has been unnecessarily added as a PSC. Click Edit and untick “PSC” to continue.';
            }

            return null;
        }

        if ($consolidatedMember->isPsc()) {
            if (!$this->doesNatureOfControlMatch($consolidatedMember, $suggestedPsc)) {
                return 'Based on the current appointments the Nature of Control selected may need to be reviewed.<br>Please Edit, go to the PSC section and click “Use Suggested Nature of Control”';
            }

            return null;
        }

        return '<span style="font-weight: 600;">Action required:</span> This officer should be listed as a PSC. Click Edit and tick “PSC” to continue.';
    }

    private function getSuggestedPscBasedOnConsolidatedMember(Company $company, ConsolidatedMember $consolidatedMember): ?PscCandidate
    {
        if (!$consolidatedMember->hasShareholders()) {
            return null;
        }

        $suggestedPscs = $this->pscSuggesterFactory->create($company)->suggest($company);
        if (empty($suggestedPscs)) {
            return null;
        }

        foreach ($suggestedPscs as $suggestedPsc) {
            if ($this->isAnyOfTheShareholders($suggestedPsc, $consolidatedMember)) {
                if ($this->hasSpecialNatureOfControlSelected($consolidatedMember)) {
                    return null;
                }

                return $suggestedPsc;
            }
        }

        return null;
    }

    private function doesNatureOfControlMatch(ConsolidatedMember $consolidatedMember, PscCandidate $pscCandidate): bool
    {
        $natureOfControl = $consolidatedMember->getNatureOfControl();
        $suggestedNatureOfControl = $pscCandidate->getNatureOfControl();

        return $natureOfControl->getOwnershipOfShares() === $suggestedNatureOfControl->getOwnershipOfShares()
            && $natureOfControl->getOwnershipOfVotingRights() === $suggestedNatureOfControl->getOwnershipOfVotingRights()
            && $natureOfControl->getRightToAppointAndRemoveDirectors() === $suggestedNatureOfControl->getRightToAppointAndRemoveDirectors()
            && $natureOfControl->getSignificantInfluenceOrControl() === $suggestedNatureOfControl->getSignificantInfluenceOrControl();
    }

    private function hasSpecialNatureOfControlSelected(ConsolidatedMember $consolidatedMember): bool
    {
        if (!$consolidatedMember->isPsc()) {
            return false;
        }

        $natureOfControl = $consolidatedMember->getNatureOfControl();

        return $natureOfControl->getRightToAppointAndRemoveDirectors()
            || $natureOfControl->getSignificantInfluenceOrControl();
    }

    private function isAnyOfTheShareholders(PscCandidate $suggestedPsc, ConsolidatedMember $consolidatedMember): bool
    {
        foreach ($consolidatedMember->getShareholders() as $shareholder) {
            if ($suggestedPsc->getShareholder()->getId() === $shareholder->getId()) {
                return true;
            }
        }

        return false;
    }
}
