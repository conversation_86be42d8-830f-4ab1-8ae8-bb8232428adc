<?php

namespace OrderModule\Processors;

use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;
use Models\Products\AnnualReturn;
use Models\Products\BasketProduct;
use CompanyFormationModule\Services\MyDetailsStepService;
use Libs\CHFiling\Core\Company as OldCompany;
use Libs\CHFiling\Core\CHFiling;
use Entities\Company;
use Entities\Customer;
use Models\Products\BundlePackage;
use Models\Products\CertificateUpgrade;
use Models\Products\CompanyNameChange;
use Models\Products\CreditProduct;
use Models\Products\DCA;
use Models\Products\MemorandumArticles;
use Models\Products\NomineeDirector;
use Models\Products\NomineeSecretary;
use Models\Products\NomineeShareholder;
use Models\Products\Package;
use Models\Products\Product;
use Models\Products\RegisterOffice;
use Models\Products\ResidentDirector;
use Models\Products\SameDayFormation;
use Models\Products\ServiceAddress;
use Repositories\CustomerRepository;
use Services\CompanyService;
use Services\OrderItemService;
use Services\OrderService;
use Legacy\Nette\Web\Session;

class OrderItemsProcessor
{
    public function __construct(
        private CHFiling $chFiling,
        private CompanyService $companyService,
        private OrderItemService $orderItemService,
        private MyDetailsStepService $myDetailsStepService,
        private Session $session,
        private CustomerRepository $customerRepository,
        private OrderService $orderService
    ) { }

    /** @param BasketProduct[] $basketItems */
    public function saveItems(Customer $customer, array $basketItems, int $orderId): void
    {
        // search for package
        foreach ($basketItems as $basketItem) {

            $basketItem->getOrderItem()->setAdditionalInformation($basketItem->getAdditionalInformation());

            if ($basketItem->getClass() == Package::class || $basketItem instanceof Package) {
                if ($basketItem->companyName == null) {
                    if ($basketItem->getId() == Package::PACKAGE_RESERVE_COMPANY_NAME) {
                        $basketItem->companyName = 'Reserve a Company Name';
                    } else {
                        $basketItem->companyName = 'Change your company name';
                    }
                }

                $company = $this->setCompany($customer, $basketItem->companyName);
                $basketItem->setCompanyId($company->getCompanyId());

                $this->resolvePrintedDocuments($customer, $company, $basketItem);

                $company->setCompanyCategory($basketItem->typeId); /** @phpstan-ignore-line */
                $company->setOrder($this->orderService->getOrderById($orderId));
                $company->setProductId($basketItem->getId());
                /** @var CompanyIncorporation $formSubmission */
                $formSubmission = $company->getFormSubmissions()->last();
                $formSubmission->setType($basketItem->typeId); /** @phpstan-ignore-line */

                // In case of RACN - set sic code to 99999
                if ($basketItem->getId() === Package::PACKAGE_RESERVE_COMPANY_NAME) {
                    $formSubmission->setSicCodes(['99999']);
                }

                if ($basketItem->getId() === Package::PACKAGE_ANNA) {
                    $formSubmission->setSicCodes($this->session->get("sicCodes") ?? []);
                    $this->session->remove("sicCodes");
                }

                // check ID system, registration and set MyDetails step
                $this->myDetailsStepService->checkRegistrationAndSet(
                    $this->companyService->getCompanyById($company->getId())
                );

                // lock company
                if ($basketItem->lockCompany) {
                    $company->setLocked(true);
                }

                // included products
                $includedProducts = $basketItem->getPackageProducts('includedProducts');
                $this->saveItemsToCompany($customer, $includedProducts, $company);

                // other products from basket
                $this->saveItemsToCompany($customer, $basketItems, $company);
                return;

                // add credit to customer's account
            } elseif ($basketItem->getClass() == CreditProduct::class) {
                $customer->addCredit($basketItem->getPrice());
                $this->customerRepository->saveEntity($customer);
            }
        }

        // save other basket items - no package there
        $this->saveItemsToCompany($customer, $basketItems);
    }

    public function saveItemsToCompany(Customer $customer, array $basketItems, Company $company = null): void
    {
        foreach ($basketItems as $basketItem) {
            $companyId = $basketItem->getCompanyId();

            if (!empty($companyId)) {
                $itemCompany = $this->companyService->getCompanyById($companyId);
            } elseif ($company) {
                $itemCompany = $company;
                $basketItem->setCompanyId($itemCompany->getCompanyId());
            } else {
                continue;
            }

            $orderItem = $basketItem->orderItem;
            if ($orderItem && !$orderItem->getCompany()) {
                $orderItem->setCompany($this->companyService->getCompanyById($itemCompany->getCompanyId()));
                $this->orderItemService->save($orderItem);
            }

            $this->applyProductEffects($basketItem, $itemCompany, $customer);
        }
    }

    private function resolvePrintedDocuments(Customer $customer, Company $company, BasketProduct $item): void
    {
        if ($item->getId() === Package::PACKAGE_BRONZE) {
            $company->setIsCertificatePrinted(true);

        } else if (in_array($item->getId(), [
            Package::PACKAGE_BASIC,
            Package::PACKAGE_PRIVACY,
            Package::PACKAGE_DIGITAL,
            Package::PACKAGE_CONTRACTOR,
            Package::PACKAGE_INTERNATIONAL,
            Package::PACKAGE_RESERVE_COMPANY_NAME,
            Package::PACKAGE_WHOLESALE_STARTER,
        ])) {
            $company->setIsCertificatePrinted(true);
            $company->setIsBronzeCoverLetterPrinted(true);
        }

        if ($customer->isWholesale() && $item->getId() === Package::PACKAGE_WHOLESALE_STARTER
            || $customer->isRetail() && $item->getId() !== Package::PACKAGE_BRONZE
        ) {
            $company->setIsBronzeCoverLetterPrinted(true);
        }

        $company->setIsMaCoverLetterPrinted(true);
        $company->setIsMaPrinted(true);

        if (in_array($item->getId(), [
            Package::PACKAGE_COMPREHENSIVE,
            Package::PACKAGE_ULTIMATE,
            Package::PACKAGE_GOLD,
            Package::PACKAGE_PLATINUM,
            Package::PACKAGE_DIAMOND,
            Package::PACKAGE_WHOLESALE_PROFESSIONAL,
        ])) {
            $company->setIsMaCoverLetterPrinted(false);
            $company->setIsMaPrinted(false);
        }

    }

    private function setCompany(Customer $customer, string $companyName): Company
    {
        $companyBuild = $this->chFiling->setCompany($customer->getId(), $companyName);
        return $this->companyService->getCompanyById($companyBuild->getId());
    }

    private function applyProductEffects(BasketProduct $basketItem, Company $itemCompany, Customer $customer): void
    {
        $productId = $basketItem->getId();

        match ($basketItem->getClass()) {
            BundlePackage::class => $this->saveItemsToCompany($customer, $basketItem->getPackageProducts('includedProducts'), $itemCompany),

            AnnualReturn::class => $this->handleAnnualReturn($basketItem, $itemCompany),

            DCA::class => $itemCompany->setDcaId($productId),

            NomineeDirector::class,
            ResidentDirector::class => $itemCompany->setNomineeDirectorId($productId),

            NomineeSecretary::class => $itemCompany->setNomineeSecretaryId($productId),

            NomineeShareholder::class => $itemCompany->setNomineeShareholderId($productId),

            RegisterOffice::class => $itemCompany->setRegisteredOfficeId($productId),

            SameDayFormation::class => $itemCompany->getIncorporationFormSubmission()?->setSameDay(true), /** @phpstan-ignore-line */

            CompanyNameChange::class => $itemCompany->setChangeNameId($productId),

            ServiceAddress::class => $itemCompany->setServiceAddressId($productId),

            MemorandumArticles::class => $itemCompany->setIsMaPrinted(false),

            CertificateUpgrade::class => $itemCompany->setIsCertificatePrinted(false),

            default => null,
        };

        match ($productId) {
            Product::DUPLICATE_CERTIFICATE_OF_INCORPORATION => $itemCompany->setIsCertificatePrinted(false),

            default => null
        };
    }

    private function handleAnnualReturn(BasketProduct $basketItem, Company $itemCompany): void
    {
        $productId = (int) $basketItem->getId();
        $oldCompany = OldCompany::getCompany($itemCompany->getCompanyId());

        if ($oldCompany->getStatus() === 'complete') {
            if (in_array($productId, [
                AnnualReturn::ANNUAL_RETURN_SERVICE_PRODUCT,
                AnnualReturn::ANNUAL_RETURN_EXPRESS_SERVICE_PRODUCT
            ])) {
                $oldCompany->setAnnualReturnId($productId);
                return;
            }

            $oldCompany->setAnnualReturn($productId);
            return;
        }

        $oldCompany->setAnnualReturnId($productId);
    }
}
