<?php

namespace ServiceCancellationModule\Controllers;

use Entities\Service;
use FrontModule\Controlers\MyServicesControler;
use Repositories\ServiceSettingsRepository;
use RouterModule\Generators\IUrlGenerator;
use RouterModule\Helpers\IControllerHelper;
use ServiceCancellationModule\Dto\CancellationData;
use ServiceCancellationModule\Facades\CancellationFacade;
use ServiceCancellationModule\Forms\CancellationForm;
use ServiceCancellationModule\Providers\CancellationResultProvider;
use ServiceCancellationModule\Renderers\RequirementsRenderer;
use ServiceCancellationModule\Repositories\ReasonDataRepository;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;
use CompanyMonitoringModule\Commands\ProcessCompanyMonitoringCommand;
use CompanyMonitoringModule\Services\CompanyTrackingsService;

class RequirementsController
{
    /**
     * @var Request
     */
    private $request;    
    
    /**
     * @var IRenderer
     */
    private $renderer;

    /**
     * @var IControllerHelper
     */
    private $controllerHelper;

    /**
     * @var ReasonDataRepository
     */
    private $reasonDataRepository;

    /**
     * @var CancellationResultProvider
     */
    private $cancellationResultProvider;

    /**
     * @var RequirementsRenderer
     */
    private $requirementsRenderer;
    
    /**
     * @var CancellationFacade
     */
    private $cancellationFacade;

    /**
     * @var ServiceSettingsRepository
     */
    private $serviceSettingsRepository;

    /**
     * @var CompanyTrackingsService
     */
    private $companyTrackingService;
    
    public function __construct(
        Request $request,
        IRenderer $renderer,
        IControllerHelper $controllerHelper,
        ReasonDataRepository $reasonDataRepository,
        CancellationResultProvider $cancellationResultProvider,
        RequirementsRenderer $requirementsRenderer,
        CancellationFacade $cancellationFacade,
        ServiceSettingsRepository $serviceSettingsRepository,
        CompanyTrackingsService $companyTrackingService
    ) {
        $this->request = $request;
        $this->renderer = $renderer;
        $this->controllerHelper = $controllerHelper;
        $this->reasonDataRepository = $reasonDataRepository;
        $this->cancellationResultProvider = $cancellationResultProvider;
        $this->requirementsRenderer = $requirementsRenderer;
        $this->cancellationFacade = $cancellationFacade;
        $this->serviceSettingsRepository = $serviceSettingsRepository;
        $this->companyTrackingService = $companyTrackingService;
    }
    
    public function check(
        Service $service,
        //used for testing
        string $officersNotOurPostcode = NULL,
        string $companyNotOurPostcode = NULL,
        string $officersResignedDate = NULL
    ): Response
    {
        return $this->renderer->render(
            [
                'service' => $service,
                'company' => $service->getCompany(),
                'officersNotOurPostcode' => $officersNotOurPostcode,
                'companyNotOurPostcode' => $companyNotOurPostcode,
                'officersResignedDate' => $officersResignedDate,
                'seo' => ['title' => 'Service Cancellation']
            ]
        );
    }

    public function result(Service $service): Response
    {
        $result = $this->cancellationResultProvider->getResult($service);
        return $this->requirementsRenderer->render($service, $result);
    }

    public function cancel(Service $service): Response
    {
        $result = $this->cancellationResultProvider->getResult($service);
        $reasonData = $this->reasonDataRepository->optional($service);
        $setting = $this->serviceSettingsRepository->getSettingsByService($service);

        if (!$reasonData || !$reasonData->getReason()) {
            $this->controllerHelper->setFlashMessage(
                'You cannot cancel service without choosing a reason',
                IControllerHelper::MESSAGE_ERROR
            );

            return $this->redirectToServices();
        }

        if ($setting->isServiceCancelled()) {
            $this->controllerHelper->setFlashMessage(
                'Service has already been cancelled',
                IControllerHelper::MESSAGE_INFO
            );
            
            return $this->redirectToServices();
        }

        if (in_array($service->getServiceTypeId(), ProcessCompanyMonitoringCommand::TRACKED_SERVICE_TYPE_IDS) &&
            $companyTracking = $this->companyTrackingService->getCompanyTrackingByServiceId($service->getId())) {
            $this->companyTrackingService->cancelCompanyTrackingService($companyTracking);
        }

        $form = $this->controllerHelper->buildForm(
            CancellationForm::class,
            new CancellationData(),
            [
                'action' => $this->controllerHelper->url(
                    'service_cancel_requirements_cancel',
                    [
                        'service' => $service->getId(),
                        //testing
                        'officersNotOurPostcode' => $this->controllerHelper->getQueryParam('officersNotOurPostcode'),
                        'companyNotOurPostcode' => $this->controllerHelper->getQueryParam('companyNotOurPostcode'),
                        'officersResignedDate' => $this->controllerHelper->getQueryParam('officersResignedDate'),
                    ]
                )
            ]
        );

        if ($result->isValid() && $form->isSubmitted() && $form->isValid()) {
            $this->cancellationFacade->cancelService($service, $reasonData);
            $this->reasonDataRepository->removeEntity($service);
            
            $company = $service->getCompany();
            $this->controllerHelper->setFlashMessage(
                sprintf('%s successfully cancelled for %s', $service->getServiceType(), $company->getName()),
                IControllerHelper::MESSAGE_SUCCESS
            );
        } else {
            $this->controllerHelper->setFlashMessage(
                'Service cannot be cancelled since you do not fulfill all the requirements',
                IControllerHelper::MESSAGE_ERROR
            );
        }

        return $this->redirectToServices();
    }

    private function redirectToServices(): RedirectResponse
    {
        return $this->controllerHelper->redirectionTo(MyServicesControler::PAGE_SERVICES, [], IUrlGenerator::OLD_LINK);
    }
}