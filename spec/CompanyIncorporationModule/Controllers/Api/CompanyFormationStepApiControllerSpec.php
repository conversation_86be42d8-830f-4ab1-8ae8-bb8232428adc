<?php

declare(strict_types=1);

namespace spec\CompanyIncorporationModule\Controllers\Api;

use CompaniesHouseModule\Repositories\CompanyIncorporationRepository;
use CompanyFormationModule\Facades\PrintedCertificateFacade;
use CompanyFormationModule\Repositories\SicCodesRepository;
use CompanyIncorporationModule\Controllers\Api\CompanyFormationStepApiController;
use CompanyIncorporationModule\Services\BusinessBankingService;
use CompanyIncorporationModule\Services\MailForwardingService;
use CompanyIncorporationModule\Services\RegisteredOfficeService;
use CompanyIncorporationModule\Services\StepUrlService;
use CompanyModule\Entities\Settings\CompanySettingList;
use CompanyModule\Entities\Settings\RegisteredOfficeUpsellSetting;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;
use Entities\CompanyHouse\Helper\Address;
use Entities\Customer;
use Models\Products\BasketProduct;
use Models\Products\Package;
use Models\Products\Product;
use PaymentModule\Factories\InlinePaymentFactory;
use PaymentModule\Views\InlinePayment\InlinePayment;
use PhpSpec\ObjectBehavior;
use PhpSpec\Wrapper\Collaborator;
use Repositories\Nodes\PackageRepository;
use RouterModule\Generators\UrlGenerator;
use Services\CompanyService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class CompanyFormationStepApiControllerSpec extends ObjectBehavior
{
    private Collaborator|UrlGenerator $urlGenerator;
    private Collaborator|CompanyService $companyService;
    private Collaborator|PrintedCertificateFacade $printedCertificateFacade;
    private Collaborator|InlinePaymentFactory $inlinePaymentFactory;
    private Collaborator|SicCodesRepository $sicCodesRepository;
    private Collaborator|CompanyIncorporationRepository $companyIncorporationRepository;
    private Collaborator|BusinessBankingService $businessBankingService;
    private Collaborator|MailForwardingService $mailForwardingService;
    private Collaborator|RegisteredOfficeService $registeredOfficeService;
    private Collaborator|PackageRepository $packageRepository;

    public function let(
        UrlGenerator $urlGenerator,
        CompanyService $companyService,
        PrintedCertificateFacade $printedCertificateFacade,
        InlinePaymentFactory $inlinePaymentFactory,
        SicCodesRepository $sicCodesRepository,
        CompanyIncorporationRepository $companyIncorporationRepository,
        BusinessBankingService $businessBankingService,
        MailForwardingService $mailForwardingService,
        RegisteredOfficeService $registeredOfficeService,
        PackageRepository $packageRepository,
    ) {
        $this->beConstructedWith(
            $urlGenerator,
            $companyService,
            $printedCertificateFacade,
            $inlinePaymentFactory,
            $sicCodesRepository,
            $companyIncorporationRepository,
            $businessBankingService,
            $mailForwardingService,
            $registeredOfficeService,
            $packageRepository,
        );

        $this->urlGenerator = $urlGenerator;
        $this->companyService = $companyService;
        $this->printedCertificateFacade = $printedCertificateFacade;
        $this->inlinePaymentFactory = $inlinePaymentFactory;
        $this->sicCodesRepository = $sicCodesRepository;
        $this->companyIncorporationRepository = $companyIncorporationRepository;
        $this->businessBankingService = $businessBankingService;
        $this->mailForwardingService = $mailForwardingService;
        $this->registeredOfficeService = $registeredOfficeService;
        $this->packageRepository = $packageRepository;
    }

    public function it_is_initializable()
    {
        $this->shouldHaveType(CompanyFormationStepApiController::class);
    }

    public function it_should_call_registered_office(
        Company $company,
        Product $fullPrivacyPackageMonthly,
        CompanyIncorporation $incorporation,
        Address $address,
        Customer $customer,
        CompanySettingList $companySettingList,
        RegisteredOfficeUpsellSetting $registeredOfficeUpsellSetting,
        InlinePayment $registeredOfficeInlinePayment,
        BasketProduct $registeredOfficeProduct,
    ) {
        $customer->getId()->willReturn(1);
        $customer->getCountryId()->willReturn(Customer::UK_CITIZEN);
        $customer->isWholesale()->willReturn(false);

        $customer->getAddress1()->willReturn('Example street 1');
        $customer->getAddress2()->willReturn('');
        $customer->getAddress3()->willReturn('');
        $customer->getCity()->willReturn('London');
        $customer->getPostcode()->willReturn('N1 123');
        $customer->getCounty()->willReturn('');
        $customer->getCountry()->willReturn('UK');

        $registeredOfficeUpsellSetting->getType()->willReturn(1);
        $companySettingList->getRegisteredOfficeUpsell()->willReturn($registeredOfficeUpsellSetting);

        $company->getId()->willReturn(1);
        $company->getCompanyId()->willReturn(1);
        $company->getCompanyName()->willReturn('Name LTD');
        $company->isIncorporated()->willReturn(false);
        $company->isDissolved()->willReturn(false);
        $company->getRegisteredOfficeId()->willReturn(null);
        $company->getCustomer()->willReturn($customer);
        $company->getSettings()->willReturn($companySettingList);
        $company->getProductId()->willReturn(Package::PACKAGE_BASIC);

        $this->registeredOfficeService->canShowUpsellOffer($company)->willReturn(true);

        $fullPrivacyPackageMonthly->getId()->willReturn(1);
        $fullPrivacyPackageMonthly->getTitle()->willReturn('Product Title');
        $fullPrivacyPackageMonthly->getPrice()->willReturn(1.1);
        $this->registeredOfficeService->getFullPrivacyPackageMonthly()->willReturn($fullPrivacyPackageMonthly);

        $company->getIncorporationFormSubmission()->willReturn($incorporation);

        $this->inlinePaymentFactory->create(
            $fullPrivacyPackageMonthly,
            $company,
            ['retryWithANewCard' => true]
        )->willReturn($registeredOfficeInlinePayment);
        $this->inlinePaymentFactory->create(
            $fullPrivacyPackageMonthly,
            $company,
            ['retryWithANewCard' => true]
        )->shouldBeCalled();

        $address->getPremise()->willReturn('Example street 1');
        $address->getStreet()->willReturn('');
        $address->getThoroughfare()->willReturn('');
        $address->getPostTown()->willReturn('London');
        $address->getCounty()->willReturn('');
        $address->getCountry()->willReturn('UK');
        $address->getPostcode()->willReturn('N1 123');
        $addressFields = [
            'premise' => 'Example street 1',
            'street' => '',
            'thoroughfare' => '',
            'postTown' => 'London',
            'county' => '',
            'country' => 'UK',
            'postcode' => 'N1 123',
        ];
        $address->getFields()->willReturn($addressFields);

        $incorporation->getRegisteredOfficeAddress()->willReturn($address);
        $incorporation->getAddresses()->willReturn([$address]);
        $incorporation->getRegisteredEmailAddress()->willReturn('<EMAIL>');
        $incorporation->getMsgAddress()->willReturn('MSG Address');
        $incorporation->isLawfulPurposeStatement()->willReturn(true);

        $this->printedCertificateFacade->getSetting($company)->willReturn(null);

        $this->registeredOfficeService->getMSGAddress()->willReturn('Address');
        $this->registeredOfficeService->getRegisteredOfficeProduct()->willReturn($registeredOfficeProduct);
        $prefillAddresses = [
            'select' => [
                'N1 123, Example street 1', 'N1 123, Example street 1'],
            'js' => [
                [
                    'premise' => 'Example street 1',
                    'street' => '',
                    'thoroughfare' => '',
                    'postTown' => 'London',
                    'county' => '',
                    'country' => 'UK',
                    'postcode' => 'N1 123',
                ],
                [
                    'premise' => 'Example street 1',
                    'street' => '',
                    'thoroughfare' => '',
                    'post_town' => 'London',
                    'county' => '',
                    'country' => 'GBR',
                    'postcode' => 'N1 123',
                    'care_of_name' => null,
                    'po_box' => null,
                ],
            ],
        ];
        $this->registeredOfficeService->getPrefillAddresses($company, [$address])->willReturn($prefillAddresses);

        $response = new JsonResponse([
            'status' => 'success',
            'registeredOfficeUpsellOffer' => true,
            'inlinePayment' => $registeredOfficeInlinePayment->getWrappedObject(),
            'prefillAddress' => $prefillAddresses,
            'addressFields' => [
                'premise' => 'Example street 1',
                'street' => '',
                'thoroughfare' => '',
                'postTown' => 'London',
                'county' => '',
                'country' => 'UK',
                'postcode' => 'N1 123',
            ],
            'registeredOfficeUpsellId' => 1,
            'registeredOfficeUpsellType' => 1,
            'registeredEmailAddressValue' => '<EMAIL>',
            'canShowNewConfirmationStatementFields' => true,
            'msgAddress' => 'Address',
            'hasMSGRegisteredOffice' => 'MSG Address',
            'hasRegisteredOfficeId' => false,
            'canShowCertificatePrintedQuestion' => false,
            'certificatePrinted' => null,
            'isWholesale' => false,
        ]);

        $this->registeredOffice($company)->shouldBeLike($response);
    }

    public function it_should_call_industry_type(
        Company $company,
    ) {
        $this->sicCodesRepository->getCHSicCodes()->willReturn(['sicCodes']);
        $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company)->willReturn(['companySicCodes']);

        $response = new JsonResponse([
            'status' => 'success',
            'sicCodes' => ['sicCodes'],
            'companySicCodes' => ['companySicCodes'],
        ]);

        $this->industryType($company)->shouldBeLike($response);
    }

    public function it_should_save_registered_office(
        Company $company,
        CompanyIncorporation $incorporation,
        Address $address,
    ) {
        $registeredOfficeFormData = '{"_token":"XV1rDSqVNOfr_ZmUKXE_dgj4wIOc4IvRQCY6DkIQSAM","registeredOfficeUpsellType":"-1","premise":"1","street":"Example street","thoroughfare":"","post_town":"London","county":"","postcode":"N1 123","country":"GB-ENG","isLawfulPurposeStatement":1,"registeredEmailAddress":"<EMAIL>"}';
        $data = json_decode($registeredOfficeFormData, true);

        $incorporation->getMsgAddress()->willReturn(false);

        $company->getId()->willReturn(1);
        $company->getIncorporationFormSubmission()->willReturn($incorporation);
        $company->getRegisteredOfficeId()->willReturn(null);

        $addressFields = [
            'premise' => '1',
            'street' => 'Example street',
            'thoroughfare' => '',
            'postTown' => 'London',
            'county' => '',
            'country' => 'UK',
            'postcode' => 'N1 123',
            'care_of_name' => '',
            'po_box' => '',
        ];

        $this->registeredOfficeService->processRegisteredOffice($data, $company, $incorporation)->willReturn($incorporation);

        $addressString = '1 Example street, London, N1 123';
        $this->registeredOfficeService->getCompanyAddress($incorporation)->willReturn($addressString);
        $this->registeredOfficeService->canShowUpsellOffer($company)->willReturn(true);

        $address->getPremise()->willReturn('1');
        $address->getStreet()->willReturn('Example street');
        $address->getThoroughfare()->willReturn('');
        $address->getPostTown()->willReturn('London');
        $address->getCounty()->willReturn('');
        $address->getCountry()->willReturn('UK');
        $address->getPostcode()->willReturn('N1 123');
        $address->getFields()->willReturn($addressFields);

        $incorporation->getRegisteredOfficeAddress()->willReturn($address);

        $incorporation->getRegisteredOfficeAddress()->shouldBeCalled();

        $response = new JsonResponse([
            'status' => 'success',
            'message' => 'Data saved successfully.',
            'addressFields' => [
                'premise' => '1',
                'street' => 'Example street',
                'thoroughfare' => '',
                'postTown' => 'London',
                'county' => '',
                'country' => 'UK',
                'postcode' => 'N1 123',
                'care_of_name' => '',
                'po_box' => '',
            ],
            'hasRegisteredOfficeId' => false,
            'registeredOfficeUpsellOffer' => true,
            'hasMSGRegisteredOffice' => false,
            'companyAddress' => $addressString,
        ]);

        $this->saveRegisteredOffice($company, $registeredOfficeFormData)->shouldBeLike($response);
    }

    public function it_should_add_sic_code(Company $company, CompanyIncorporation $incorporation)
    {
        $company->getId()->willReturn(1);
        $company->getSicCodes()->willReturn([]);
        $company->getIncorporationFormSubmission()->willReturn($incorporation);

        $company->setSicCodes(['12345'])->shouldBeCalled();
        $this->companyService->saveCompany($company)->shouldBeCalled();

        $sicCodesWithDescription = [
            'Code' => '12345',
            'Description' => 'Description',
        ];
        $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company)->willReturn($sicCodesWithDescription);

        $response = new JsonResponse([
            'status' => 'success',
            'message'  => 'SIC code saved successfully.',
            'companySicCodes' => $sicCodesWithDescription,
        ]);

        $codeData = '{"code":"12345","description":"Description"}';

        $this->addSicCode($company, $codeData)->shouldBeLike($response);
    }

    public function it_should_add_not_sic_code_already_added(Company $company)
    {
        $company->getSicCodes()->willReturn(['22345', '12345', '31234']);

        $expectedResponse = [
            'status' => 'error',
            'error' => 'SIC code already added to this company',
        ];

        $errorResponse = new JsonResponse(
            $expectedResponse,
            Response::HTTP_BAD_REQUEST
        );

        $codeData = '{"code":"12345","description":"Description"}';

        $this->addSicCode($company, $codeData)->shouldBeLike($errorResponse);
    }

    public function it_should_add_not_sic_code_max_length(Company $company)
    {
        $company->getSicCodes()->willReturn(['12346', '22345', '31234', '42342']);

        $expectedResponse = [
            'status' => 'error',
            'error' => 'You already have 4 SIC codes associated to your company',
        ];

        $errorResponse = new JsonResponse(
            $expectedResponse,
            Response::HTTP_BAD_REQUEST
        );

        $codeData = '{"code":"12345","description":"Description"}';

        $this->addSicCode($company, $codeData)->shouldBeLike($errorResponse);
    }

    public function it_should_remove_sic_code(Company $company, CompanyIncorporation $incorporation)
    {
        $company->getId()->willReturn(1);
        $company->getIncorporationFormSubmission()->willReturn($incorporation);

        $company->getSicCodes()->willReturn(['11111', '12345']);
        $this->sicCodesRepository->remove(1, '12345')->shouldBeCalled();

        $sicCodesWithDescription = [
            'Code' => '11111',
            'Description' => 'Description',
        ];
        $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company)->willReturn($sicCodesWithDescription);

        $response = new JsonResponse([
            'status' => 'success',
            'message'  => 'SIC code removed successfully.',
            'companySicCodes' => $sicCodesWithDescription,
        ]);

        $codeData = '{"code":"12345","description":"Description"}';

        $this->removeSicCode($company, $codeData)->shouldBeLike($response);
    }

    public function it_should_save_business_banking_offer(Company $company)
    {
        $company->getId()->willReturn(1);
        $this->businessBankingService->saveOffers($company, ['productIds' => [123]])->shouldBeCalled();

        $this->urlGenerator->url(
            StepUrlService::APPOINTMENTS_PAGE,
            ['company' => 1]
        )->willReturn('url');

        $response = new JsonResponse([
            'status' => 'success',
            'message'  => 'Offers saved successfully.',
            'redirect_url' => 'url',
        ]);

        $selectedOffers = '{"productIds":[123]}';

        $this->saveBusinessBankingOffer($company, $selectedOffers)->shouldBeLike($response);
    }

    public function it_should_process_mail_forwarding_event(
        Company $company,
        Customer $customer,
        InlinePayment $inlinePayment,
        Product $product,
        Product $node,
    ) {
        $company->getId()->willReturn(1);
        $company->getCustomer()->willReturn($customer);
        $company->isDissolved()->willReturn(false);

        $node->getId()->willReturn(1);

        $product->getPrice()->willReturn(1.11);
        $product->getName()->willReturn('Product name');

        $inlinePayment->getOmnipayComponentData()->willReturn('data');
        $inlinePayment->getOmnipayUrl()->willReturn('url');
        $inlinePayment->getStatus()->willReturn('status');
        $inlinePayment->isOnFailureRetryWithANewCard()->willReturn(false);
        $inlinePayment->doesntHaveTokens()->willReturn(false);

        $this->mailForwardingService->createOffer($company)->willReturn([
            'inlinePayment' => $inlinePayment,
            'product' => $product,
            'productNodeId' => 1,
            'productPrice' => 1.11,
            'productName' => 'Product name',
        ]);

        $response = new JsonResponse([
            'status' => 'success',
            'omnipayComponentData' => 'data',
            'omnipayUrl' => 'url',
            'paymentStatus' => 'status',
            'isOnInitRetryNewCard' => false,
            'product' => $product->getWrappedObject(),
        ]);

        $this->retrieveMailForwardingData($company)->shouldBeLike($response);
    }
}
