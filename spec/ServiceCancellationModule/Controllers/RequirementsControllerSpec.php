<?php

namespace spec\ServiceCancellationModule\Controllers;

use CompanyModule\Domain\Company\CompanyName;
use CompanyMonitoringModule\Services\CompanyTrackingsService;
use Entities\Company;
use Entities\Service;
use Entities\ServiceSettings;
use FrontModule\Controlers\MyServicesControler;
use Prophecy\Argument;
use Repositories\ServiceSettingsRepository;
use RouterModule\Generators\IUrlGenerator;
use RouterModule\Helpers\IControllerHelper;
use ServiceCancellationModule\Controllers\RequirementsController;
use PhpSpec\ObjectBehavior;
use ServiceCancellationModule\Dto\CancellationData;
use ServiceCancellationModule\Dto\ReasonData;
use ServiceCancellationModule\Entities\CancellationResult;
use ServiceCancellationModule\Entities\Reason;
use ServiceCancellationModule\Facades\CancellationFacade;
use ServiceCancellationModule\Forms\CancellationForm;
use ServiceCancellationModule\Providers\CancellationResultProvider;
use ServiceCancellationModule\Renderers\RequirementsRenderer;
use ServiceCancellationModule\Repositories\ReasonDataRepository;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

class RequirementsControllerSpec extends ObjectBehavior
{
    /**
     * @var Request
     */
    private $request;

    /**
     * @var IRenderer
     */
    private $renderer;

    /**
     * @var IControllerHelper
     */
    private $controllerHelper;

    /**
     * @var ReasonDataRepository
     */
    private $reasonDataRepository;

    /**
     * @var CancellationResultProvider
     */
    private $cancellationResultProvider;

    /**
     * @var RequirementsRenderer
     */
    private $requirementsRenderer;

    /**
     * @var CancellationFacade
     */
    private $cancellationFacade;

    /**
     * @var ServiceSettingsRepository
     */
    private $serviceSettingsRepository;

    /**
     * @var CompanyTrackingsService
     */
    private $companyTrackingService;

    function let(
        IRenderer $renderer,
        IControllerHelper $controllerHelper,
        ReasonDataRepository $reasonDataRepository,
        CancellationResultProvider $cancellationResultProvider,
        RequirementsRenderer $requirementsRenderer,
        CancellationFacade $cancellationFacade,
        ServiceSettingsRepository $serviceSettingsRepository,
        CompanyTrackingsService $companyTrackingService
    ) {
        $request = new Request();
        $this->beConstructedWith($request, $renderer, $controllerHelper, $reasonDataRepository, $cancellationResultProvider, $requirementsRenderer, $cancellationFacade, $serviceSettingsRepository, $companyTrackingService);
        $this->request = $request;
        $this->renderer = $renderer;
        $this->controllerHelper = $controllerHelper;
        $this->reasonDataRepository = $reasonDataRepository;
        $this->cancellationResultProvider = $cancellationResultProvider;
        $this->requirementsRenderer = $requirementsRenderer;
        $this->cancellationFacade = $cancellationFacade;
        $this->serviceSettingsRepository = $serviceSettingsRepository;
        $this->companyTrackingService = $companyTrackingService;
    }
    
    function it_is_initializable()
    {
        $this->shouldHaveType(RequirementsController::class);
    }
    
    function it_should_render_page_with_ajax_loader(Service $service, Company $company, Response $response)
    {
        $service->getCompany()->willReturn($company);
        $this->renderer->render([
            'service' => $service,
            'company' => $company,
            'officersNotOurPostcode' => NULL,
            'companyNotOurPostcode' => NULL,
            'officersResignedDate' => NULL,
            'seo' => ['title' => 'Service Cancellation']
        ])->willReturn($response);
        $this->check($service)->shouldReturn($response);
    }
    
    function it_should_render_page_with_requirements_result(Service $service, CancellationResult $cancellationResult, Response $response)
    {
        $this->cancellationResultProvider->getResult($service)->willReturn($cancellationResult);
        $this->requirementsRenderer->render($service, $cancellationResult)->willReturn($response);
        $this->result($service)->shouldReturn($response);
    }
    
    function it_should_cancel_service(Service $service, Company $company, CancellationResult $cancellationResult, ReasonData $reasonData, ServiceSettings $serviceSettings, Reason $reason, FormInterface $form, RedirectResponse $redirectResponse)
    {
        $this->cancellationResultProvider->getResult($service)->willReturn($cancellationResult);
        $this->reasonDataRepository->optional($service)->willReturn($reasonData);
        $this->serviceSettingsRepository->getSettingsByService($service)->willReturn($serviceSettings);
        
        $reasonData->getReason()->willReturn($reason);
        $serviceSettings->isServiceCancelled()->willReturn(FALSE);

        $service->getId()->willReturn('123');
        $this->controllerHelper->url('service_cancel_requirements_cancel', Argument::any())->willReturn('tralala');
        $this->controllerHelper->getQueryParam(Argument::any())->willReturn(NULL);
        $this->controllerHelper->buildForm(CancellationForm::class, new CancellationData(), ['action' => 'tralala'])->willReturn($form);

        $cancellationResult->isValid()->willReturn(TRUE);
        $form->isSubmitted()->willReturn(true);
        $form->isValid()->willReturn(TRUE);

        $this->cancellationFacade->cancelService($service, $reasonData)->shouldBeCalled();
        $this->reasonDataRepository->removeEntity($service)->shouldBeCalled();
        $service->getServiceTypeId()->willReturn('FRAUD_PROTECTION');
        $service->getCompany()->willReturn($company);
        $service->getServiceType()->willReturn('Ultra type');

        $companyName = CompanyName::uppercased('I LOVE YOU LTD');
        $company->getName()->willReturn($companyName);
        $this->controllerHelper->setFlashMessage('Ultra type successfully cancelled for I LOVE YOU LTD', IControllerHelper::MESSAGE_SUCCESS)->shouldBeCalled();
        $this->controllerHelper->redirectionTo(MyServicesControler::PAGE_SERVICES, [], IUrlGenerator::OLD_LINK)->willReturn($redirectResponse);
        
        $this->cancel($service)->shouldReturn($redirectResponse);
    }
    
    function it_should_try_to_cancel_service_but_fail_since_requirements_were_not_fulfilled(Service $service, CancellationResult $cancellationResult, ReasonData $reasonData, ServiceSettings $serviceSettings, Reason $reason, FormInterface $form, RedirectResponse $redirectResponse)
    {
        $this->cancellationResultProvider->getResult($service)->willReturn($cancellationResult);
        $this->reasonDataRepository->optional($service)->willReturn($reasonData);
        $this->serviceSettingsRepository->getSettingsByService($service)->willReturn($serviceSettings);
        
        $reasonData->getReason()->willReturn($reason);
        $serviceSettings->isServiceCancelled()->willReturn(FALSE);

        $service->getId()->willReturn('123');
        $this->controllerHelper->getQueryParam(Argument::any())->willReturn(NULL);
        $this->controllerHelper->url('service_cancel_requirements_cancel', Argument::any())->willReturn('tralala');
        $this->controllerHelper->buildForm(CancellationForm::class, new CancellationData(), ['action' => 'tralala'])->willReturn($form);
        
        $cancellationResult->isValid()->willReturn(FALSE);
        $form->isSubmitted()->willReturn(true);
        $form->isValid()->willReturn(TRUE);
        $service->getServiceTypeId()->willReturn('FRAUD_PROTECTION');
        $this->controllerHelper->setFlashMessage('Service cannot be cancelled since you do not fulfill all the requirements', IControllerHelper::MESSAGE_ERROR)->shouldBeCalled();
        $this->controllerHelper->redirectionTo(MyServicesControler::PAGE_SERVICES, [], IUrlGenerator::OLD_LINK)->willReturn($redirectResponse);
        
        $this->cancel($service)->shouldReturn($redirectResponse);
    }
    
    function it_should_try_to_cancel_service_but_fail_since_service_is_already_cancelled(Service $service, CancellationResult $cancellationResult, ReasonData $reasonData, ServiceSettings $serviceSettings, Reason $reason, RedirectResponse $redirectResponse)
    {
        $this->cancellationResultProvider->getResult($service)->willReturn($cancellationResult);
        $this->reasonDataRepository->optional($service)->willReturn($reasonData);
        $this->serviceSettingsRepository->getSettingsByService($service)->willReturn($serviceSettings);
        
        $reasonData->getReason()->willReturn($reason);
        $serviceSettings->isServiceCancelled()->willReturn(TRUE);

        $this->controllerHelper->setFlashMessage('Service has already been cancelled', IControllerHelper::MESSAGE_INFO)->shouldBeCalled();
        $this->controllerHelper->redirectionTo(MyServicesControler::PAGE_SERVICES, [], IUrlGenerator::OLD_LINK)->willReturn($redirectResponse);
        
        $this->cancel($service)->shouldReturn($redirectResponse);
    }
    
    function it_should_try_to_cancel_service_but_fail_since_no_reason_has_been_selected(Service $service, CancellationResult $cancellationResult, ServiceSettings $serviceSettings, RedirectResponse $redirectResponse)
    {
        $this->cancellationResultProvider->getResult($service)->willReturn($cancellationResult);
        $this->reasonDataRepository->optional($service)->willReturn(NULL);
        $this->serviceSettingsRepository->getSettingsByService($service)->willReturn($serviceSettings);
        
        $this->controllerHelper->setFlashMessage('You cannot cancel service without choosing a reason', IControllerHelper::MESSAGE_ERROR)->shouldBeCalled();
        $this->controllerHelper->redirectionTo(MyServicesControler::PAGE_SERVICES, [], IUrlGenerator::OLD_LINK)->willReturn($redirectResponse);
        
        $this->cancel($service)->shouldReturn($redirectResponse);
    }
}
