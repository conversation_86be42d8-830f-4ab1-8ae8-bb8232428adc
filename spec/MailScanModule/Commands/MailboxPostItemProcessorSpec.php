<?php

declare(strict_types=1);

namespace spec\MailScanModule\Commands;

use CompanyModule\Entities\Settings\MailForwardingAddressSetting;
use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use CompanyModule\Facades\MailForwardingFacade;
use CompanyModule\Facades\PostItemHandlingFacade;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use Entities\Customer;
use Entities\Service;
use Exceptions\Business\BasketException;
use Exceptions\Technical\NodeException;
use MailScanModule\ApiClient\IMailroomApiClient;
use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Commands\MailboxPostItemProcessor;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Deciders\MailboxUpdateStatusDecider;
use MailScanModule\Deciders\ReleaseItemDecider;
use MailScanModule\Emailers\MailboxEmailer;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Exceptions\NotAMailboxProductException;
use MailScanModule\Facades\MailForwardingAddressFacade;
use MailScanModule\Factories\PostItemBagFactory;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Services\PostItemService;
use Models\Products\BasketProduct;
use Models\Products\Product;
use PaymentModule\Services\ChargeService;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Psr\Log\LoggerInterface;
use Services\CompanyService;
use Services\NodeService;
use tests\helpers\ObjectHelper;
use tests\helpers\ProductHelper;
use tests\helpers\ServicesHelper;
use Utils\Date;

class MailboxPostItemProcessorSpec extends ObjectBehavior
{
    private const MOCK_COMPANY_NUMBER = '12345678';

    /**
     * @var ChargeService
     */
    private $paymentService;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var IMailroomApiClient
     */
    private $mailroomApiClient;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var MailboxTierDecider
     */
    private $mailboxTierDecider;

    /**
     * @var MailForwardingFacade
     */
    private $mailForwardingFacade;

    /**
     * @var NodeService
     */
    private $nodeService;

    /**
     * @var MailboxEmailer
     */
    private $emailer;

    /**
     * @var PostItemHandlingFacade
     */
    private $postItemHandlingFacade;

    /**
     * @var ReleaseItemDecider
     */
    private $releaseItemDecider;

    /**
     * @var PostItemService
     */
    private $postItemService;

    /**
     * @var MailForwardingAddressFacade
     */
    private $mailForwardingAddressFacade;

    /**
     * @var MailboxUpdateStatusDecider
     */
    private $mailboxUpdateStatusDecider;

    public function let(
        ChargeService $paymentService,
        CompanyService $companyService,
        MailroomApiClient $mailroomApiClient,
        LoggerInterface $logger,
        MailboxTierDecider $mailboxTierDecider,
        MailForwardingFacade $mailForwardingFacade,
        NodeService $nodeService,
        MailboxEmailer $emailer,
        PostItemHandlingFacade $postItemHandlingFacade,
        ReleaseItemDecider $releaseItemDecider,
        PostItemService $postItemService,
        MailForwardingAddressFacade $mailForwardingAddressFacade,
        MailboxUpdateStatusDecider $mailboxUpdateStatusDecider,
    ) {
        $this->beConstructedWith(
            $paymentService,
            $companyService,
            $mailroomApiClient,
            $logger,
            $mailboxTierDecider,
            $mailForwardingFacade,
            $nodeService,
            $emailer,
            $postItemHandlingFacade,
            $releaseItemDecider,
            $postItemService,
            $mailForwardingAddressFacade,
            $mailboxUpdateStatusDecider
        );

        $this->paymentService = $paymentService;
        $this->companyService = $companyService;
        $this->mailroomApiClient = $mailroomApiClient;
        $this->logger = $logger;
        $this->mailboxTierDecider = $mailboxTierDecider;
        $this->mailForwardingFacade = $mailForwardingFacade;
        $this->nodeService = $nodeService;
        $this->emailer = $emailer;
        $this->postItemHandlingFacade = $postItemHandlingFacade;
        $this->releaseItemDecider = $releaseItemDecider;
        $this->postItemService = $postItemService;
        $this->mailForwardingAddressFacade = $mailForwardingAddressFacade;
        $this->mailboxUpdateStatusDecider = $mailboxUpdateStatusDecider;
        $this->companyService->clearDoctrineMemory()->willReturn(null);
        $this->mailForwardingFacade->clearDoctrineMemory()->willReturn(null);
        $this->postItemHandlingFacade->clearDoctrineMemory()->willReturn(null);

    }

    public function it_is_initializable(): void
    {
        $this->mockNodeIds();

        $this->shouldHaveType(MailboxPostItemProcessor::class);
    }

    public function it_should_process_none_when_no_items(): void
    {
        // First call returns empty bag, which will cause loop to exit immediately
        $this->mailroomApiClient
            ->getUnprocessedPostItems(argument::type('int'), argument::any())
            ->willReturn(PostItemBagFactory::createFromArray([]));

        $this->checkScriptResults(
            $this->buildExpectedResults([])
        );
    }

    public function it_should_not_process_post_item_if_no_company_found(): void
    {
        $mockItems = $this->getMockItems();

        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            PostItemBagFactory::createFromArray($mockItems),
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn(null);
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_NO_COMPANY_ID => 1,
                ]
            )
        );
    }

    public function it_should_not_process_post_item_if_id_check_for_company_is_incomplete(): void
    {
        $mockItems = $this->getMockItems();

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(
            new Customer('<EMAIL>', 'test'),
            '1 LTD'
        );

        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(false);
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_NO_ID_CHECK => 1,
                ]
            )
        );
    }

    public function it_should_not_process_items_if_the_company_does_not_have_a_service(): void
    {
        $mockItems = $this->getMockItems();

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(
            new Customer('<EMAIL>', 'test'),
            '1 LTD'
        );

        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_NO_SERVICE => 1,
                ]
            )
        );
    }

    public function it_should_not_process_items_if_the_service_is_overdue(): void
    {
        $mockItems = $this->getMockItems();

        $items = PostItemBagFactory::createFromArray($mockItems);
        $customer = new Customer('<EMAIL>', 'test');
        $company = new Company(
            $customer,
            '1 LTD'
        );

        $order = ObjectHelper::createOrder($customer, 1, new \DateTime('-380 days'));
        $orderItem = ObjectHelper::createOrderItem($order);
        $service = ObjectHelper::createServiceFromType(Service::TYPE_REGISTERED_OFFICE, 165, $company, $orderItem, new \DateTime('-375 days'), new Date('-10 days'));
        $company->addService($service);

        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_SERVICE_OVERDUE => 1,
                ]
            )
        );
    }

    public function it_should_not_process_items_if_there_are_charging_attempts_and_retry_payments_is_false(): void
    {
        $mockItems = $this->getMockItems();
        $mockItems[0]['details'] = [
            'charging_attempts' => '1',
        ];

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(new Customer(
            '<EMAIL>', 'test'),
            '1 LTD'
        );

        $service = ServicesHelper::getService(company: $company);
        $service->setProduct(ProductHelper::getMailboxProduct());
        $service->setDtStart(new \DateTime());
        $service->setDtExpires(new \DateTime('+1 year'));
        $company->addService($service);

        $product = new BasketProduct(Product::PRODUCT_REGISTERED_OFFICE);
        $product->setMailboxAcceptanceNonStatutory(1);
        $product->setMailboxQuotaNonStatutoryScanned(10);
        $product->setMailboxSettingPostItem(1);
        $product->setMailboxHandlingFeePostItem(2.0);
        $product->setMailboxForwardingFeePostItem(3.0);

        $this->postItemService->getProductByTier(Argument::any(), Argument::any())->willReturn($product);
        $this->mailboxTierDecider->determineMailboxTier(Argument::any(), Argument::any(), Argument::any())->willReturn(1);
        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->nodeService->requiredProductByName(Argument::any())->willReturn($product);
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->mailForwardingFacade->getQuotasByType($company, Argument::any(), Argument::any())->willReturn(1);
        $this->postItemHandlingFacade->getHandlingSettingByType(
            $company,
            Argument::any(),
            $product,
            Argument::any()
        )->willReturn(1);
        $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->willReturn(new MailForwardingAddressSetting($company, '1', '2', '3', '4', '5', '6'));

        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->postItemHandlingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingAddressFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_UNPAID_CHARGES => 1,
                    MailboxPostItemProcessor::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . 1 => 1,
                ]
            )
        );
    }

    public function it_should_process_items_if_type_is_accepted_and_has_unlimited_quotas(): void
    {
        $mockItems = $this->getMockItems();

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(
            new Customer('<EMAIL>', 'test'),
            '1 LTD'
        );

        $service = ServicesHelper::getService(company: $company);
        $service->setProduct(ProductHelper::getMailboxProduct());
        $service->setDtStart(new \DateTime());
        $service->setDtExpires(new \DateTime('+1 year'));

        $company->addService($service);

        $product = new BasketProduct(Product::PRODUCT_REGISTERED_OFFICE);
        $product->setMailboxAcceptanceNonStatutory(1);
        $product->setMailboxQuotaNonStatutoryCollect(-1);
        $product->setMailboxQuotaMailCollect(-1);
        $product->setMailboxSettingPostItem(1);
        $product->setMailboxHandlingFeePostItem(2.0);
        $product->setMailboxForwardingFeePostItem(3.0);

        $this->mailboxTierDecider->determineMailboxTier(Argument::any())->willReturn(MailboxTierDecider::TIER_1);
        $this->postItemService->getProductByTier(MailboxTierDecider::TIER_1, Argument::any())->willReturn($product);
        $this->postItemService->hasUnlimitedQuotas($product, Argument::any(), Argument::any())->willReturn(true);
        $this->mailForwardingFacade->addQuotas($company, 1, Argument::any(), Argument::any(), Argument::any())->willReturn(1);
        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->nodeService->requiredProductByName(Argument::any())->willReturn($product);
        $this->postItemHandlingFacade->getPostItemHandlingSetting($company)->willReturn(new PostItemHandlingSetting($company, 1, 1));
        $this->postItemHandlingFacade->getHandlingSettingByType($company, Argument::any(), $product, MailboxTierDecider::TIER_1)->willReturn(3);
        $this->nodeService->requiredProductByName('mailbox-post-item-handling-fee')->willReturn($this->mockHandlingFeeProduct());
        $this->mailForwardingFacade->getQuotasByType($company, Argument::any(), Argument::any())->willReturn(0);
        $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->willReturn(new MailForwardingAddressSetting($company, '1', '2', '3', '4', '5', '6'));
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->postItemHandlingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingAddressFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_RELEASED => 1,
                    MailboxPostItemProcessor::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . 1 => 1,
                ]
            )
        );
    }

    public function it_should_process_items_if_type_is_accepted_and_has_unlimited_quotas_with_forwarding_fee_when_posted(): void
    {
        $mockItems = $this->getMockItems();

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(
            new Customer('<EMAIL>', 'test'),
            '1 LTD'
        );

        $service = ServicesHelper::getService(company: $company);

        $product = new BasketProduct(0);
        $product->setMailboxAcceptanceNonStatutory(1);
        $product->setMailboxQuotaNonStatutoryPost(-1);
        $product->setMailboxSettingPostItem(3);
        $product->setMailboxHandlingFeePostItem(2.0);
        $product->setMailboxForwardingFeePostItem(3.0);

        $service->setProduct(ProductHelper::getMailboxProduct(
            nodeId: 0,
            renewalProductId: 0,
            name: Product::PRODUCT_MAILBOX_PREMIUM_INITIAL
        ));
        $service->setDtStart(new \DateTime());
        $service->setDtExpires(new \DateTime('+1 year'));

        $company->addService($service);

        $this->mailboxTierDecider->determineMailboxTier(Argument::any(), Argument::any(), Argument::any())->willReturn(MailboxTierDecider::TIER_3);
        $this->postItemService->getProductByTier(MailboxTierDecider::TIER_3, Argument::any())->willReturn($product);
        $this->postItemService->hasUnlimitedQuotas($product, Argument::any(), Argument::any())->willReturn(true);
        $this->mailForwardingFacade->addQuotas($company, 1, Argument::any(), Argument::any(), Argument::any())->willReturn(1);
        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL)->willReturn($product);
        $this->nodeService->requiredProductByName('mailbox-post-item-handling-fee')->willReturn($this->mockHandlingFeeProduct());
        $this->nodeService->requiredProductByName('mailbox-post-item-forwarding-fee')->willReturn($this->mockForwardingFeeProduct());
        $this->postItemHandlingFacade->getPostItemHandlingSetting($company)->willReturn(new PostItemHandlingSetting($company, 3, 1));
        $this->postItemHandlingFacade->getHandlingSettingByType($company, Argument::any(), $product, MailboxTierDecider::TIER_3)->willReturn(3);
        $this->mailForwardingFacade->getQuotasByType($company, Argument::any(), Argument::any())->willReturn(0);
        $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->willReturn(new MailForwardingAddressSetting($company, '1', '2', '3', '4', '5', '6'));
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->postItemHandlingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingAddressFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_RELEASED => 1,
                    MailboxPostItemProcessor::TEST_KEY_FORWARDING_CHARGES => 1,
                    MailboxPostItemProcessor::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . 3 => 1,
                ]
            )
        );
    }

    public function it_should_process_items_if_there_are_charging_attempts_and_retry_payments_is_true(): void
    {
        $mockItems = $this->getMockItems();
        $mockItems[0]['details'] = [
            'charging_attempts' => '1',
        ];

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(
            new Customer('<EMAIL>', 'test'),
            '1 LTD'
        );

        $service = ServicesHelper::getService(company: $company);

        $product = new BasketProduct(0);
        $product->setMailboxAcceptanceNonStatutory(1);
        $product->setMailboxQuotaNonStatutoryPost(-1);
        $product->setMailboxSettingPostItem(1);
        $product->setMailboxHandlingFeePostItem(2.0);

        $service->setProduct(ProductHelper::getMailboxProduct(
            nodeId: Product::PRODUCT_REGISTERED_OFFICE,
            renewalProductId: Product::PRODUCT_REGISTERED_OFFICE,
            name: Product::PRODUCT_REGISTERED_OFFICE
        ));
        $service->setDtStart(new \DateTime());
        $service->setDtExpires(new \DateTime('+1 year'));

        $company->addService($service);

        $handlingFeeProduct = $this->mockHandlingFeeProduct();

        $this->mailboxTierDecider->determineMailboxTier(Argument::any(), Argument::any(), Argument::any())->willReturn(MailboxTierDecider::TIER_1);
        $this->postItemService->getProductByTier(MailboxTierDecider::TIER_1, Argument::any())->willReturn($product);
        $this->postItemService->hasUnlimitedQuotas($product, Argument::any(), Argument::any())->willReturn(false);
        $this->postItemService->isWithinQuotaMaximum($company, $product, Argument::any(), Argument::any())->willReturn(true);
        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE)->willReturn($product);
        $this->nodeService->requiredProductByName('mailbox-post-item-handling-fee')->willReturn($this->mockHandlingFeeProduct());
        $this->postItemHandlingFacade->getPostItemHandlingSetting($company)->willReturn(new PostItemHandlingSetting($company, 1, 1));
        $this->postItemHandlingFacade->getHandlingSettingByType($company, Argument::any(), $product, MailboxTierDecider::TIER_1)->willReturn(3);
        $this->mailForwardingFacade->getQuotasByType($company, Argument::any(), Argument::any())->willReturn(0);
        $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->willReturn(new MailForwardingAddressSetting($company, '1', '2', '3', '4', '5', '6'));
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->postItemHandlingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingAddressFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_RELEASED => 1,
                    MailboxPostItemProcessor::DEBUG_KEY_RELEASED_WITHOUT_CHARGES => 1,
                    MailboxPostItemProcessor::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . 1 => 1,
                ]
            ),
            true
        );
    }

    /**
     * @throws NotAMailboxProductException
     * @throws NonUniqueResultException
     * @throws NodeException
     * @throws BasketException
     */
    public function it_should_process_if_within_quota_limit_and_increase_quota_usage(): void
    {
        $mockItems = $this->getMockItems();

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(
            new Customer('<EMAIL>', 'test'),
            '1 LTD'
        );

        $service = ServicesHelper::getService(company: $company);

        $product = new BasketProduct(0);
        $product->setMailboxAcceptanceNonStatutory(1);
        $product->setMailboxQuotaNonStatutoryCollect(10);
        $product->setMailboxSettingPostItem(1);
        $product->setMailboxHandlingFeePostItem(2.0);

        $service->setProduct(ProductHelper::getMailboxProduct(
            nodeId: 0,
            renewalProductId: 0,
            name: Product::PRODUCT_MAILBOX_PREMIUM_INITIAL
        ));
        $service->setDtStart(new \DateTime());
        $service->setDtExpires(new \DateTime('+1 year'));

        $company->addService($service);

        $handlingFeeProduct = $this->mockHandlingFeeProduct();

        $this->mailboxTierDecider->determineMailboxTier(Argument::any(), Argument::any(), Argument::any())->willReturn(MailboxTierDecider::TIER_3);
        $this->postItemService->getProductByTier(MailboxTierDecider::TIER_3, Argument::any())->willReturn($product);
        $this->postItemService->hasUnlimitedQuotas($product, Argument::any(), Argument::any())->willReturn(false);
        $this->postItemService->isWithinQuotaMaximum($company, $product, Argument::any(), Argument::any())->willReturn(PostItemService::WITHIN_TYPE_QUOTA);
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL)->willReturn($product);
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE)->willReturn($product);
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL)->willReturn($product);
        $this->nodeService->requiredProductByName('mailbox-post-item-handling-fee')->willReturn($this->mockHandlingFeeProduct());
        $this->postItemHandlingFacade->getPostItemHandlingSetting($company)->willReturn(new PostItemHandlingSetting($company, 1, 1));
        $this->postItemHandlingFacade->getHandlingSettingByType($company, Argument::any(), $product, MailboxTierDecider::TIER_3)->willReturn(1);
        $this->mailForwardingFacade->getQuotasByType($company, MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED, PostItemTypeEnum::TYPE_NON_STATUTORY->value)->willReturn(3);
        $this->mailForwardingFacade->addQuotas($company, 1, PostItemTypeEnum::TYPE_NON_STATUTORY->value, MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED, Argument::any())->willReturn(4);
        $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->willReturn(new MailForwardingAddressSetting($company, '1', '2', '3', '4', '5', '6'));
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->postItemHandlingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingAddressFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_RELEASED => 1,
                    MailboxPostItemProcessor::TEST_KEY_TOTAL_QUOTAS_AMOUNT => 1,
                    MailboxPostItemProcessor::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . 3 => 1,
                ]
            ),
        );
    }

    public function it_should_charge_extra_quota_if_over_quota_limit(): void
    {
        $mockItems = $this->getMockItems();

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(
            new Customer('<EMAIL>', 'test'),
            '1 LTD'
        );

        $service = ServicesHelper::getService(company: $company);

        $product = new BasketProduct(0);
        $product->setMailboxAcceptanceNonStatutory(1);
        $product->setMailboxQuotaNonStatutoryCollect(10);
        $product->setMailboxSettingPostItem(1);
        $product->setMailboxExtraQuotaPostItemFee(5);
        $product->setMailboxHandlingFeePostItem(2.0);

        $service->setProduct(ProductHelper::getMailboxProduct(
            nodeId: 0,
            renewalProductId: 0,
            name: Product::PRODUCT_MAILBOX_PREMIUM_INITIAL
        ));
        $service->setDtStart(new \DateTime());
        $service->setDtExpires(new \DateTime('+1 year'));

        $company->addService($service);

        $this->mailboxTierDecider->determineMailboxTier(Argument::any(), Argument::any(), Argument::any())->willReturn(MailboxTierDecider::TIER_3);
        $this->postItemService->getProductByTier(MailboxTierDecider::TIER_3, Argument::any())->willReturn($product);
        $this->postItemService->hasUnlimitedQuotas($product, Argument::any(), Argument::any())->willReturn(false);
        $this->postItemService->isWithinQuotaMaximum($company, $product, Argument::any(), Argument::any())->willReturn(false);
        // First call returns items, second call returns empty bag to terminate loop
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE)->willReturn($product);
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL)->willReturn($product);
        $this->nodeService->requiredProductByName('mailbox-post-item-handling-fee')->willReturn($this->mockHandlingFeeProduct());
        $this->nodeService->requiredProductByName('mail_forwarding_extra_quota')->willReturn($this->mockExtraQuotaFeeProduct());
        $this->postItemHandlingFacade->getPostItemHandlingSetting($company)->willReturn(new PostItemHandlingSetting($company, 1, 1));
        $this->postItemHandlingFacade->getHandlingSettingByType($company, Argument::any(), $product, MailboxTierDecider::TIER_3)->willReturn(1);
        $this->mailForwardingFacade->getQuotasByType($company, MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED, PostItemTypeEnum::TYPE_NON_STATUTORY->value)->willReturn(10);
        $this->mailForwardingFacade->addQuotas($company, 1, PostItemTypeEnum::TYPE_NON_STATUTORY->value, MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED, Argument::any())->willReturn(11);
        $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->willReturn(new MailForwardingAddressSetting($company, '1', '2', '3', '4', '5', '6'));
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->postItemHandlingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingAddressFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_RELEASED => 1,
                    MailboxPostItemProcessor::TEST_KEY_EXTRA_QUOTA_CHARGES => 1,
                    MailboxPostItemProcessor::TEST_KEY_HANDLING_FEE_CHARGES => 1,
                    MailboxPostItemProcessor::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . 3 => 1
                ]
            ),
        );
    }

    public function it_should_not_process_items_if_forwarding_address_is_not_valid_and_processing_method_is_post(): void
    {
        $mockItems = $this->getMockItems();

        $items = PostItemBagFactory::createFromArray($mockItems);

        $company = new Company(
            new Customer('<EMAIL>', 'test'),
            '1 LTD'
        );

        $service = ServicesHelper::getService(company: $company);

        $product = new BasketProduct(0);
        $product->setMailboxAcceptanceNonStatutory(1);
        $product->setMailboxQuotaNonStatutoryScanned(10);
        $product->setMailboxQuotaNonStatutoryPost(10);
        $product->setMailboxSettingPostItem(1);
        $product->setMailboxHandlingFeePostItem(2.0);

        $service->setProduct(ProductHelper::getMailboxProduct(
            nodeId: 0,
            renewalProductId: 0,
            name: Product::PRODUCT_MAILBOX_PREMIUM_INITIAL
        ));
        $service->setDtStart(new \DateTime());
        $service->setDtExpires(new \DateTime('+1 year'));

        $company->addService($service);

        $this->mailboxTierDecider->determineMailboxTier(Argument::any(), Argument::any(), Argument::any())->willReturn(MailboxTierDecider::TIER_3);
        $this->postItemService->getProductByTier(MailboxTierDecider::TIER_3, Argument::any())->willReturn($product);
        $this->postItemService->hasUnlimitedQuotas($product, Argument::any(), Argument::any())->willReturn(false);
        $this->postItemService->isWithinQuotaMaximum($company, $product, Argument::any(), Argument::any())->willReturn(true);
        $this->mailroomApiClient->getUnprocessedPostItems(
            argument::type('int'),
            argument::any()
        )->willReturn(
            $items,
            PostItemBagFactory::createFromArray([])
        );
        $this->companyService->getCompanyByCompanyNumber(self::MOCK_COMPANY_NUMBER)->willReturn($company);
        $this->releaseItemDecider->isIdCheckCompleted($company)->willReturn(true);
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE)->willReturn($product);
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL)->willReturn($product);
        $this->nodeService->requiredProductByName('mailbox-post-item-handling-fee')->willReturn($this->mockHandlingFeeProduct());
        $this->postItemHandlingFacade->getPostItemHandlingSetting($company)->willReturn(new PostItemHandlingSetting($company, 1, 1));
        $this->postItemHandlingFacade->getHandlingSettingByType($company, Argument::any(), $product, MailboxTierDecider::TIER_3)->willReturn(3); // 3 = POST
        $this->mailForwardingFacade->getQuotasByType($company, Argument::any(), Argument::any())->willReturn(0);
        $invalidAddressSetting = new MailForwardingAddressSetting($company);
        $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->willReturn($invalidAddressSetting);
        $this->companyService->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->postItemHandlingFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailForwardingAddressFacade->clearDoctrineMemory()->shouldBeCalled();
        $this->mailboxUpdateStatusDecider->shouldBeSecurelyDestroyed(argument::any())->willReturn(false);
        $this->emailer->sendOneEmailForEachItem(
            argument::type(Company::class),
            argument::type('array'),
            argument::type('string'),
            argument::any()
        )->shouldBeCalled()->willReturn([]);

        $this->checkScriptResults(
            $this->buildExpectedResults(
                [
                    MailboxPostItemProcessor::DEBUG_KEY_NO_FORWARDING_ADDRESS => 1,
                    MailboxPostItemProcessor::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . 3 => 1
                ]
            ),
        );
    }

    /**
     * @throws NodeException
     * @throws NonUniqueResultException
     * @throws BasketException
     */
    private function checkScriptResults(array $expectedResults, bool $retryPayments = false, bool $debug = true): void
    {
        $result = $this->process(retryPayments: $retryPayments, debug: $debug);
        foreach ($expectedResults as $key => $value) {
            $result[$key]->shouldBe($value);
        }
    }

    private function mockHandlingFeeProduct(): BasketProduct
    {
        return new BasketProduct(1);
    }

    private function mockForwardingFeeProduct(): BasketProduct
    {
        return new BasketProduct(1);
    }

    private function mockExtraQuotaFeeProduct(): BasketProduct
    {
        return new BasketProduct(1);
    }

    private function mockNodeIds(): void
    {
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE_N1_MONTHLY)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE_N_1_MONTHLY_RENEWAL_FEE)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE_BASKET_UPSELL)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE_SERVICE)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE_SERVICE_ADDRESS_BUNDLE)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_REGISTER_OFFICE_EC1_N1_RENEWAL)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_STANDARD_INITIAL)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_STANDARD_RENEWAL)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_PREMIUM_RENEWAL)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL)->willReturn(ProductHelper::getPrivacyPackage());
        $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_RENEWAL)->willReturn(ProductHelper::getPrivacyPackage());
    }

    private function getMockItems(): array
    {
        return [
            [
                'post_item_id' => 'asd-123-dfg-345-sdf',
                'company_name' => 'COMPANY NAME LTD',
                'company_number' => self::MOCK_COMPANY_NUMBER,
                'type' => PostItemTypeEnum::TYPE_NON_STATUTORY->value,
                'sender' => 'sender',
                'file_name' => 'filename.pdf',
                'batch_number' => '123',
                'operator' => 'CMS',
                'details' => [],
                'events' => [],
                'status' => 'status',
                'dtc' => ['date' => '2025-01-01'],
            ]
        ];
    }

    private function buildExpectedResults(array $values = []): array
    {
        $testKeys = [
            MailboxPostItemProcessor::TEST_KEY_FORWARDING_CHARGES,
            MailboxPostItemProcessor::TEST_KEY_TOTAL_QUOTAS_AMOUNT,
            MailboxPostItemProcessor::TEST_KEY_EXTRA_QUOTA_CHARGES,
        ];

        $processingKeys = [
            MailboxPostItemProcessor::DEBUG_KEY_NO_COMPANY_ID,
            MailboxPostItemProcessor::DEBUG_KEY_NO_ID_CHECK,
            MailboxPostItemProcessor::DEBUG_KEY_SERVICE_OVERDUE,
            MailboxPostItemProcessor::DEBUG_KEY_NO_SERVICE,
            MailboxPostItemProcessor::DEBUG_KEY_UNPAID_CHARGES,
            MailboxPostItemProcessor::DEBUG_KEY_RELEASED,
            MailboxPostItemProcessor::DEBUG_KEY_FAILED_TO_CHARGE,
            MailboxPostItemProcessor::DEBUG_KEY_RTS,
            MailboxPostItemProcessor::DEBUG_KEY_WAITING_PAYMENT,
            MailboxPostItemProcessor::TEST_KEY_FORWARDING_CHARGES,
            MailboxPostItemProcessor::TEST_KEY_TOTAL_QUOTAS_AMOUNT,
            MailboxPostItemProcessor::TEST_KEY_EXTRA_QUOTA_CHARGES,
            MailboxPostItemProcessor::DEBUG_KEY_NO_FORWARDING_ADDRESS,
        ];

        $totalValue = 0;
        foreach ($processingKeys as $key) {
            if (isset($values[$key])) {
                $processingTotals[$key] = $values[$key];
                if (!in_array($key, $testKeys)) {
                    $totalValue += $values[$key];
                }
            }
        }

        if (isset($values[MailboxPostItemProcessor::DEBUG_KEY_RELEASED_WITHOUT_CHARGES])) {
            $processingTotals[MailboxPostItemProcessor::DEBUG_KEY_RELEASED_WITHOUT_CHARGES] =
                $values[MailboxPostItemProcessor::DEBUG_KEY_RELEASED_WITHOUT_CHARGES];
        }

        if (isset($values[MailboxPostItemProcessor::DEBUG_KEY_RELEASED_WITH_CHARGES])) {
            $processingTotals[MailboxPostItemProcessor::DEBUG_KEY_RELEASED_WITH_CHARGES] =
                $values[MailboxPostItemProcessor::DEBUG_KEY_RELEASED_WITH_CHARGES];
        }

        $processingTotals[MailboxPostItemProcessor::DEBUG_KEY_TOTAL] = $totalValue;

        for ($tier = 1; $tier <= 4; $tier++) {
            $tierKey = MailboxPostItemProcessor::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . $tier;
            if (isset($values[$tierKey])) {
                $processingTotals[$tierKey] = $values[$tierKey];
            }
        }

        return $processingTotals;
    }
}
