<?php

declare(strict_types=1);

namespace spec\MailScanModule\Commands;

use MailScanModule\Commands\MailboxTrialEndingEmailCommand;
use MailScanModule\Emailers\MailboxEmailer;
use Monolog\Logger;
use PhpSpec\ObjectBehavior;
use Services\EventService;
use Services\NodeService;
use Services\ServiceService;

class MailboxTrialEndingEmailCommandSpec extends ObjectBehavior
{
    public function let(
        ServiceService $serviceService,
        NodeService $nodeService,
        Logger $logger,
        MailboxEmailer $mailboxEmailer,
        EventService $eventService
    ): void {
        $this->beConstructedWith(
            $serviceService,
            $nodeService,
            $logger,
            $mailboxEmailer,
            $eventService
        );
    }

    public function it_is_initializable(): void
    {
        $this->shouldHaveType(MailboxTrialEndingEmailCommand::class);
    }

    public function it_sends_trial_ending_emails_with_default_parameters(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        EventService $eventService
    ): void {
        $product1 = new class() extends ObjectBehavior {
            public function getId(): int { return 1; }
        };
        $product2 = new class() extends ObjectBehavior {
            public function getId(): int { return 2; }
        };

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1->getWrappedObject());
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2->getWrappedObject());

        $service = createServiceMock();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $mailboxEmailer->prepareEmail($service)->willReturn(true);
        $mailboxEmailer->sendEmail()->willReturn(true);
        $eventService->recordEvent('mailbox.trial_ending_email', $service)->willReturn(true);

        $this->sendTrialEndingEmails()->shouldNotThrow();
    }

    public function it_handles_dry_run_mode(
        MailboxEmailer $mailboxEmailer,
        EventService $eventService
    ): void {
        $mailboxEmailer->prepareEmail()->shouldNotBeCalled();
        $mailboxEmailer->sendEmail()->willReturn(true);
        $eventService->recordEvent()->shouldNotBeCalled();

        $this->sendTrialEndingEmails(7, true)->shouldNotThrow();
    }

    public function it_handles_custom_days_before_parameter(
        ServiceService $serviceService,
        NodeService $nodeService
    ): void {
        $product1 = new class() extends ObjectBehavior {
            public function getId(): int { return 1; }
        };
        $product2 = new class() extends ObjectBehavior {
            public function getId(): int { return 2; }
        };

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1->getWrappedObject());
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2->getWrappedObject());

        $serviceService->getServicesExpiringWithinDays([5], [1, 2], 'mailbox.trial_ending_email')->willReturn([]);

        $this->sendTrialEndingEmails(5)->shouldNotThrow();
    }

    public function it_handles_email_preparation_failure(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        Logger $logger
    ): void {
        $product1 = new class() extends ObjectBehavior {
            public function getId(): int { return 1; }
        };
        $product2 = new class() extends ObjectBehavior {
            public function getId(): int { return 2; }
        };

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1->getWrappedObject());
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2->getWrappedObject());

        $service = $this->createServiceMock();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $mailboxEmailer->prepareEmail($service)->willReturn(false);
        $logger->error('Failed to prepare email for service', ['serviceId' => $service->getId()])->shouldBeCalled();

        $this->sendTrialEndingEmails()->shouldNotThrow();
    }

    public function it_handles_email_sending_failure(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        Logger $logger
    ): void {
        $product1 = new class() extends ObjectBehavior {
            public function getId(): int { return 1; }
        };
        $product2 = new class() extends ObjectBehavior {
            public function getId(): int { return 2; }
        };

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1->getWrappedObject());
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2->getWrappedObject());

        $service = $this->createServiceMock();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $mailboxEmailer->prepareEmail()->willReturn(true);
        $mailboxEmailer->sendEmail()->willReturn(false);
        $logger->error('Failed to send email for service', ['serviceId' => $service->getId()])->shouldBeCalled();

        $this->sendTrialEndingEmails()->shouldNotThrow();
    }

    public function it_handles_event_recording_failure(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        EventService $eventService,
        Logger $logger
    ): void {
        $product1 = new class() extends ObjectBehavior {
            public function getId(): int { return 1; }
        };
        $product2 = new class() extends ObjectBehavior {
            public function getId(): int { return 2; }
        };

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1->getWrappedObject());
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2->getWrappedObject());

        $service = $this->createServiceMock();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $mailboxEmailer->prepareEmail()->willReturn(true);
        $mailboxEmailer->sendEmail()->willReturn(true);
        $eventService->recordEvent('mailbox.trial_ending_email', $service)->willReturn(false);
        $logger->error('Failed to record event for service', ['serviceId' => $service->getId()])->shouldBeCalled();

        $this->sendTrialEndingEmails()->shouldNotThrow();
    }

    public function it_throws_node_exception_when_product_not_found(
        NodeService $nodeService
    ): void {
        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willThrow(new \Exceptions\Technical\NodeException('Product not found'));

        $this->shouldThrow(\Exceptions\Technical\NodeException::class)->duringSendTrialEndingEmails();
    }

    public function it_throws_exception_when_service_retrieval_fails(
        ServiceService $serviceService,
        NodeService $nodeService
    ): void {
        $product1 = new class() extends ObjectBehavior {
            public function getId(): int { return 1; }
        };
        $product2 = new class() extends ObjectBehavior {
            public function getId(): int { return 2; }
        };

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1->getWrappedObject());
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2->getWrappedObject());

        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willThrow(new \Exception('Service retrieval failed'));

        $this->shouldThrow(\Exception::class)->during('sendTrialEndingEmails', []);
    }
}