<?php

declare(strict_types=1);

namespace spec\MailScanModule\Commands;

use EmailModule\IEmailLog;
use Entities\Customer;
use Entities\Service;
use MailScanModule\Commands\MailboxTrialEndingEmailCommand;
use MailScanModule\Emailers\MailboxEmailer;
use Models\Products\Product;
use Monolog\Logger;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Services\EventService;
use Services\NodeService;
use Services\ServiceService;

class MailboxTrialEndingEmailCommandSpec extends ObjectBehavior
{
    public function let(
        ServiceService $serviceService,
        NodeService $nodeService,
        Logger $logger,
        MailboxEmailer $mailboxEmailer,
        EventService $eventService
    ): void {
        $this->beConstructedWith(
            $serviceService,
            $nodeService,
            $logger,
            $mailboxEmailer,
            $eventService
        );
    }

    public function it_is_initializable(): void
    {
        $this->shouldHaveType(MailboxTrialEndingEmailCommand::class);
    }

    public function it_sends_trial_ending_emails_with_default_parameters(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        EventService $eventService,
        IEmailLog $emailLog,
        Product $product1,
        Product $product2
    ): void {
        $product1->getId()->willReturn(1);
        $product2->getId()->willReturn(2);

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1);
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2);

        $service = $this->createServiceMock();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $emailLog->getId()->willReturn(123);
        $mailboxEmailer->sendTrialEndingEmail(Argument::type('array'))->willReturn($emailLog);
        $eventService->notify('mailbox.trial_ending_email', $service->getId())->shouldBeCalled();

        $this->sendTrialEndingEmails()->shouldNotThrow();
    }

    public function it_handles_dry_run_mode(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        EventService $eventService,
        Product $product1,
        Product $product2
    ): void {
        $product1->getId()->willReturn(1);
        $product2->getId()->willReturn(2);

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1);
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2);

        $service = $this->createServiceMock();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $mailboxEmailer->sendTrialEndingEmail(Argument::any())->shouldNotBeCalled();
        $eventService->notify(Argument::any(), Argument::any())->shouldNotBeCalled();

        $this->sendTrialEndingEmails(7, true)->shouldNotThrow();
    }

    public function it_handles_custom_days_before_parameter(
        ServiceService $serviceService,
        NodeService $nodeService,
        Product $product1,
        Product $product2
    ): void {
        $product1->getId()->willReturn(1);
        $product2->getId()->willReturn(2);

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1);
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2);

        $serviceService->getServicesExpiringWithinDays([5], [1, 2], 'mailbox.trial_ending_email')->willReturn([]);

        $this->sendTrialEndingEmails(5)->shouldNotThrow();
    }

    public function it_handles_email_preparation_failure(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        Logger $logger,
        Product $product1,
        Product $product2
    ): void {
        $product1->getId()->willReturn(1);
        $product2->getId()->willReturn(2);

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1);
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2);

        // Create a service that will cause email preparation to fail
        $service = $this->createServiceMockWithMissingData();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $logger->error('Failed to prepare email data', Argument::type('array'))->shouldBeCalled();

        $this->sendTrialEndingEmails()->shouldNotThrow();
    }

    public function it_handles_email_sending_failure(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        Logger $logger,
        Product $product1,
        Product $product2
    ): void {
        $product1->getId()->willReturn(1);
        $product2->getId()->willReturn(2);

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1);
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2);

        $service = $this->createServiceMock();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $mailboxEmailer->sendTrialEndingEmail(Argument::type('array'))->willThrow(new \Exception('Email sending failed'));
        $logger->error('Email sending failed', Argument::type('array'))->shouldBeCalled();

        $this->sendTrialEndingEmails()->shouldNotThrow();
    }

    public function it_handles_event_recording_failure(
        ServiceService $serviceService,
        NodeService $nodeService,
        MailboxEmailer $mailboxEmailer,
        EventService $eventService,
        Logger $logger,
        IEmailLog $emailLog,
        Product $product1,
        Product $product2
    ): void {
        $product1->getId()->willReturn(1);
        $product2->getId()->willReturn(2);

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1);
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2);

        $service = $this->createServiceMock();
        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willReturn([$service]);

        $emailLog->getId()->willReturn(123);
        $mailboxEmailer->sendTrialEndingEmail(Argument::type('array'))->willReturn($emailLog);
        $eventService->notify('mailbox.trial_ending_email', $service->getId())->willThrow(new \Exception('Event recording failed'));
        $logger->error('Failed to record event', Argument::type('array'))->shouldBeCalled();

        $this->sendTrialEndingEmails()->shouldNotThrow();
    }

    public function it_throws_node_exception_when_product_not_found(
        NodeService $nodeService
    ): void {
        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willThrow(new \Exceptions\Technical\NodeException('Product not found'));

        $this->shouldThrow(\Exceptions\Technical\NodeException::class)->duringSendTrialEndingEmails();
    }

    public function it_throws_exception_when_service_retrieval_fails(
        ServiceService $serviceService,
        NodeService $nodeService,
        Product $product1,
        Product $product2
    ): void {
        $product1->getId()->willReturn(1);
        $product2->getId()->willReturn(2);

        $nodeService->requiredProductByName('mailbox-standard-initial-3-months')->willReturn($product1);
        $nodeService->requiredProductByName('mailbox-standard-privacy-initial-1-month')->willReturn($product2);

        $serviceService->getServicesExpiringWithinDays([7], [1, 2], 'mailbox.trial_ending_email')->willThrow(new \Exception('Service retrieval failed'));

        $this->shouldThrow(\Exception::class)->during('sendTrialEndingEmails', []);
    }

    private function createServiceMock()
    {
        $service = new class {
            public function getId() { return 1; }
            public function getCustomer() {
                return new class {
                    public function getId() { return 1; }
                    public function getFirstName() { return 'John'; }
                    public function getEmail() { return '<EMAIL>'; }
                };
            }
            public function getProduct() {
                return new class {
                    public function getServiceTypeId() { return 1; }
                };
            }
            public function getRenewalProduct() {
                return new class {
                    public function getPrice() { return 1999; }
                };
            }
            public function getCompany() {
                return new class {
                    public function getActiveCorePackageService() {
                        return new class {
                            public function getServiceTypeId() { return 2; }
                        };
                    }
                };
            }
            public function getDtExpires() {
                return new \DateTime('+7 days');
            }
            public function getInitialDuration() {
                return '+3 months';
            }
        };

        return $service;
    }

    private function createServiceMockWithMissingData()
    {
        $service = new class {
            public function getId() { return 1; }
            public function getCustomer() {
                return new class {
                    public function getId() { return 1; }
                    public function getFirstName() { return 'John'; }
                    public function getEmail() { return '<EMAIL>'; }
                };
            }
            public function getProduct() {
                return null; // This will cause preparation failure
            }
            public function getRenewalProduct() {
                return null; // This will cause preparation failure
            }
            public function getCompany() {
                return null; // This will cause preparation failure
            }
            public function getDtExpires() {
                return new \DateTime('+7 days');
            }
            public function getInitialDuration() {
                return '+3 months';
            }
        };

        return $service;
    }
}