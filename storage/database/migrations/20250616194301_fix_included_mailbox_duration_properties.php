<?php
declare(strict_types=1);

use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Product;
use Phinx\Migration\AbstractMigration;

final class FixIncludedMailboxDurationProperties extends AbstractMigration
{
    public function up(): void
    {
        $helper = new NodeMigrationHelper($this);

        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL_12_MONTHS), 'duration', '+12 months');
        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_INITIAL_3_MONTHS), 'duration', '+3 months');
    }

    public function down(): void
    {
        $helper = new NodeMigrationHelper($this);

        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL_12_MONTHS), 'duration', '+12 months');
        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_INITIAL_3_MONTHS), 'duration', '+3 months');
    }
}
