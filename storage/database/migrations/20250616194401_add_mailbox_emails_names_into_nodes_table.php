<?php
declare(strict_types=1);

use MailScanModule\Providers\MailboxEmailNameProvider;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Product;
use Phinx\Migration\AbstractMigration;

final class AddMailboxEmailsNamesIntoNodesTable extends AbstractMigration
{
    const MAILBOX_EMAILS = [
        MailboxEmailNameProvider::MAIL_NO_ID_CHECK,
        MailboxEmailNameProvider::MAIL_NO_SERVICE_AND_NO_ID_CHECK,
        MailboxEmailNameProvider::MAIL_NO_SERVICE,
        MailboxEmailNameProvider::MAIL_RELEASED_COLLECT,
        MailboxEmailNameProvider::MAIL_RELEASED_POST,
        MailboxEmailNameProvider::MAIL_RELEASED_SCAN,
        MailboxEmailNameProvider::MAIL_SERVICE_OVERDUE_AND_NO_CHECK_ID,
        MailboxEmailNameProvider::MAIL_SERVICE_OVERDUE,
        MailboxEmailNameProvider::MAIL_WAITING_PAYMENT_SCAN,
        MailboxEmailNameProvider::MISSING_FORWARDING_ADDRESS,
        MailboxEmailNameProvider::PARCEL_NO_ID_CHECK,
        MailboxEmailNameProvider::PARCEL_NO_SERVICE_AND_NO_ID_CHECK,
        MailboxEmailNameProvider::PARCEL_NO_SERVICE,
        MailboxEmailNameProvider::PARCEL_RELEASED_COLLECT_REMINDER,
        MailboxEmailNameProvider::PARCEL_RELEASED_COLLECT,
        MailboxEmailNameProvider::PARCEL_RTS,
        MailboxEmailNameProvider::PARCEL_SERVICE_OVERDUE_AND_NO_CHECK_ID,
        MailboxEmailNameProvider::PARCEL_SERVICE_OVERDUE,
        MailboxEmailNameProvider::PARCEL_WAITING_PAYMENT_POST_REMINDER,
        MailboxEmailNameProvider::PARCEL_WAITING_PAYMENT_POST,
        MailboxEmailNameProvider::TRIAL_ENDING_EMAIL,
        MailboxEmailNameProvider::UNPAID_CHARGE,
    ];

    public function up()
    {
        $helper = new NodeMigrationHelper($this);
        $mailboxEmailsFolderId = $helper->getExistingNodeId(CreateMailboxEmailsFolder::FOLDER_NAME)->getId();
        $order = 10;

        foreach (self::MAILBOX_EMAILS as $mailboxEmailName) {
            $page = new Page($this->convertToTitleCase($mailboxEmailName));
            $properties = [
                new Property('from', '<EMAIL>'),
                new Property('fromName', 'Companies Made Simple'),
                new Property('tag1', 'mailbox-email'),
                new Property('tag2', $mailboxEmailName),
                new Property('templateName', $mailboxEmailName)
            ];

            $node = new Node(
                $page,
                $properties,
                $mailboxEmailName,
                $mailboxEmailsFolderId,
                null,
                'EmailAdminControler',
                $order+10
            );

            $helper->create($node);
        }
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);

        foreach (self::MAILBOX_EMAILS as $mailboxEmailName) {
            $nodeId = $helper->getExistingNodeId($mailboxEmailName)->getId();
            $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
            $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
            $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
        }
    }

    private function convertToTitleCase(string $string): string
    {
        $parts = explode('-', $string);

        $convertedParts = array_map(function ($part) {
            return ucfirst(strtolower($part));
        }, $parts);

        return implode(' ', $convertedParts);
    }
}
