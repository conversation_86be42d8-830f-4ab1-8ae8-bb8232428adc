<?php

declare(strict_types=1);

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class CreateMailboxEmailsFolder extends AbstractMigration
{
    public const FOLDER_NAME = 'mailbox_emails';
    private const FOLDER_TITLE = 'Mailbox Emails';

    public function up(): void
    {
        $helper = new NodeMigrationHelper($this);

        if ($helper->getExistingNodeId(self::FOLDER_NAME)) {
            return;
        }

        $page = new Page(self::FOLDER_TITLE);

        $node = new Node(
            $page,
            [],
            self::FOLDER_NAME,
            137,
            null,
            'FolderAdminControler',
            30
        );

        $helper->create($node);
    }

    public function down(): void
    {
        $nodeId = (new NodeMigrationHelper($this))->getExistingNodeId(self::FOLDER_NAME);

        if (empty($nodeId)) {
            return;
        }

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
    }
}
